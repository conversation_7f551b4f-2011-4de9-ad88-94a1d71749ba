"""
智能修复协调器 - 整合智能路由器和执行器

核心思想：
1. 推理模型分析问题复杂度 → 智能路由决策
2. 简单问题 → 自动化执行器（快速修复）
3. 复杂问题 → Aider执行器（AI深度思考）
4. 充分发挥每个组件的优势
"""

import logging
from typing import Dict, Any, Optional
import time
import asyncio

from .intelligent_fix_router import IntelligentFixRouter, FixStrategy, FixComplexity
from ..executors.aider_executor import AiderExecutor
from ..utils.conversation_logger import global_conversation_logger, ConversationStatus
from .performance_monitor import global_performance_monitor

logger = logging.getLogger(__name__)

class IntelligentFixCoordinator:
    """智能修复协调器"""

    def __init__(self):
        self.router = IntelligentFixRouter()
        self.aider_executor = AiderExecutor()

    async def execute_intelligent_fix(self, error_analysis: Dict[str, Any],
                                    project_path: str,
                                    session_id: str,
                                    job_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行智能修复流程

        Args:
            error_analysis: 错误分析结果
            project_path: 项目路径
            session_id: 会话ID
            job_info: 作业信息

        Returns:
            修复结果
        """
        try:
            logger.info("🚀 启动智能修复协调器...")

            # 第1步：智能路由分析（带性能监控和缓存）
            start_time = time.time()

            # 使用缓存的项目上下文收集
            async def gather_context():
                return await self._gather_project_context(project_path, job_info)

            project_context = await global_performance_monitor.cached_operation(
                operation_name="gather_project_context",
                cache_key_data={'project_path': project_path, 'job_info': job_info},
                operation_func=gather_context
            )

            # 使用受控的AI调用进行路由分析
            async def route_analysis():
                return await self.router.analyze_and_route(error_analysis, project_context)

            fix_decision = await global_performance_monitor.controlled_ai_call(
                operation_name="intelligent_fix_routing",
                ai_call_func=route_analysis
            )

            routing_duration = time.time() - start_time

            # 记录路由决策
            global_conversation_logger.log_round(
                session_id=session_id,
                round_name=f"智能修复路由决策",
                prompt=f"错误分析: {error_analysis}",
                response=f"""
## 🧠 智能修复路由决策

### 📊 分析结果
- **复杂度**: {fix_decision.complexity.value}
- **策略**: {fix_decision.strategy.value}
- **置信度**: {fix_decision.confidence:.2f}
- **预估时间**: {fix_decision.estimated_time}秒
- **需要审查**: {fix_decision.requires_human_review}

### 🎯 决策理由
{fix_decision.reasoning}

### 📝 执行计划
{self._format_execution_plan(fix_decision)}
""",
                model_name="intelligent-fix-router",
                duration=routing_duration,
                status=ConversationStatus.SUCCESS
            )

            # 第2步：根据策略执行修复
            if fix_decision.strategy == FixStrategy.AUTOMATED:
                return await self._execute_automated_fix(fix_decision, project_path, session_id)
            elif fix_decision.strategy == FixStrategy.AIDER_GUIDED:
                return await self._execute_aider_fix(fix_decision, project_path, session_id, error_analysis)
            else:  # HYBRID
                return await self._execute_hybrid_fix(fix_decision, project_path, session_id, error_analysis)

        except Exception as e:
            logger.error(f"智能修复协调器执行失败: {e}")
            return {
                'success': False,
                'message': f'智能修复协调器失败: {str(e)}',
                'strategy': 'error',
                'complexity': 'unknown',
                'execution_time': 0
            }

    async def _gather_project_context(self, project_path: str, job_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """收集深度项目上下文信息"""
        try:
            import os
            import json
            from pathlib import Path

            context = {
                'project_path': project_path,
                'project_type': 'python',  # 默认
                'job_info': job_info or {}
            }

            # 1. 检测项目类型和结构
            project_files = self._scan_project_files(project_path)
            context.update(project_files)

            # 2. 读取配置文件
            config_info = await self._read_config_files(project_path)
            context['configurations'] = config_info

            # 3. 分析依赖信息
            dependency_info = await self._analyze_dependencies(project_path, context['project_type'])
            context['dependencies'] = dependency_info

            # 4. 收集Git信息
            git_info = await self._collect_git_info(project_path)
            context['git_info'] = git_info

            # 5. 分析代码质量配置
            quality_config = self._analyze_quality_config(project_path)
            context['quality_config'] = quality_config

            return context

        except Exception as e:
            logger.error(f"收集项目上下文失败: {e}")
            return {'project_path': project_path, 'project_type': 'unknown'}

    def _scan_project_files(self, project_path: str) -> Dict[str, Any]:
        """扫描项目文件结构"""
        import os

        files_info = {
            'has_tests': False,
            'has_docs': False,
            'has_ci_config': False,
            'config_files': [],
            'project_type': 'python'
        }

        try:
            # 检查常见目录
            for root, dirs, files in os.walk(project_path):
                # 只扫描前两层目录
                level = root.replace(project_path, '').count(os.sep)
                if level > 2:
                    continue

                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_path)

                    # 检测项目类型
                    if file == 'package.json':
                        files_info['project_type'] = 'javascript'
                    elif file == 'Cargo.toml':
                        files_info['project_type'] = 'rust'
                    elif file == 'go.mod':
                        files_info['project_type'] = 'go'
                    elif file == 'pom.xml':
                        files_info['project_type'] = 'java'

                    # 检测配置文件
                    config_files = [
                        '.flake8', 'setup.cfg', 'pyproject.toml', 'tox.ini',
                        '.editorconfig', '.gitignore', '.pre-commit-config.yaml',
                        'requirements.txt', 'requirements-dev.txt', 'Pipfile',
                        'docker-compose.yml', 'Dockerfile', '.gitlab-ci.yml'
                    ]

                    if file in config_files:
                        files_info['config_files'].append(rel_path)

                    # 检测CI配置
                    if file in ['.gitlab-ci.yml', '.github', 'Jenkinsfile', '.travis.yml']:
                        files_info['has_ci_config'] = True

                # 检查目录
                for dir_name in dirs:
                    if dir_name in ['tests', 'test', '__tests__']:
                        files_info['has_tests'] = True
                    elif dir_name in ['docs', 'doc', 'documentation']:
                        files_info['has_docs'] = True

        except Exception as e:
            logger.error(f"扫描项目文件失败: {e}")

        return files_info

    async def _read_config_files(self, project_path: str) -> Dict[str, Any]:
        """读取配置文件内容"""
        import os
        import configparser
        import json

        configs = {}

        try:
            # 读取 .flake8 配置
            flake8_path = os.path.join(project_path, '.flake8')
            if os.path.exists(flake8_path):
                try:
                    config = configparser.ConfigParser()
                    config.read(flake8_path)
                    configs['flake8'] = dict(config.items('flake8')) if config.has_section('flake8') else {}
                except Exception as e:
                    logger.warning(f"读取.flake8配置失败: {e}")

            # 读取 pyproject.toml 配置
            pyproject_path = os.path.join(project_path, 'pyproject.toml')
            if os.path.exists(pyproject_path):
                try:
                    import toml
                    with open(pyproject_path, 'r', encoding='utf-8') as f:
                        configs['pyproject'] = toml.load(f)
                except Exception as e:
                    logger.warning(f"读取pyproject.toml配置失败: {e}")

            # 读取 package.json 配置
            package_json_path = os.path.join(project_path, 'package.json')
            if os.path.exists(package_json_path):
                try:
                    with open(package_json_path, 'r', encoding='utf-8') as f:
                        configs['package_json'] = json.load(f)
                except Exception as e:
                    logger.warning(f"读取package.json配置失败: {e}")

        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")

        return configs

    async def _analyze_dependencies(self, project_path: str, project_type: str) -> Dict[str, Any]:
        """分析项目依赖"""
        import os

        dependencies = {
            'main_dependencies': [],
            'dev_dependencies': [],
            'python_version': None,
            'dependency_files': []
        }

        try:
            if project_type == 'python':
                # 分析 requirements.txt
                req_path = os.path.join(project_path, 'requirements.txt')
                if os.path.exists(req_path):
                    dependencies['dependency_files'].append('requirements.txt')
                    with open(req_path, 'r', encoding='utf-8') as f:
                        dependencies['main_dependencies'] = [
                            line.strip() for line in f.readlines()
                            if line.strip() and not line.startswith('#')
                        ]

                # 分析 requirements-dev.txt
                req_dev_path = os.path.join(project_path, 'requirements-dev.txt')
                if os.path.exists(req_dev_path):
                    dependencies['dependency_files'].append('requirements-dev.txt')
                    with open(req_dev_path, 'r', encoding='utf-8') as f:
                        dependencies['dev_dependencies'] = [
                            line.strip() for line in f.readlines()
                            if line.strip() and not line.startswith('#')
                        ]

        except Exception as e:
            logger.error(f"分析依赖失败: {e}")

        return dependencies

    async def _collect_git_info(self, project_path: str) -> Dict[str, Any]:
        """收集Git信息"""
        git_info = {
            'is_git_repo': False,
            'current_branch': None,
            'recent_commits': [],
            'modified_files': []
        }

        try:
            import subprocess

            # 检查是否是Git仓库
            result = subprocess.run(
                ['git', 'rev-parse', '--git-dir'],
                cwd=project_path,
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                git_info['is_git_repo'] = True

                # 获取当前分支
                result = subprocess.run(
                    ['git', 'branch', '--show-current'],
                    cwd=project_path,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    git_info['current_branch'] = result.stdout.strip()

                # 获取最近的提交
                result = subprocess.run(
                    ['git', 'log', '--oneline', '-5'],
                    cwd=project_path,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    git_info['recent_commits'] = result.stdout.strip().split('\n')

        except Exception as e:
            logger.warning(f"收集Git信息失败: {e}")

        return git_info

    def _analyze_quality_config(self, project_path: str) -> Dict[str, Any]:
        """分析代码质量配置"""
        import os

        quality_config = {
            'has_linting': False,
            'has_formatting': False,
            'has_type_checking': False,
            'linting_tools': [],
            'formatting_tools': [],
            'type_checking_tools': []
        }

        try:
            # 检查配置文件
            if os.path.exists(os.path.join(project_path, '.flake8')):
                quality_config['has_linting'] = True
                quality_config['linting_tools'].append('flake8')

            if os.path.exists(os.path.join(project_path, 'pyproject.toml')):
                # 检查 pyproject.toml 中的工具配置
                try:
                    import toml
                    with open(os.path.join(project_path, 'pyproject.toml'), 'r') as f:
                        config = toml.load(f)

                    if 'tool' in config:
                        tools = config['tool']

                        if 'black' in tools:
                            quality_config['has_formatting'] = True
                            quality_config['formatting_tools'].append('black')

                        if 'mypy' in tools:
                            quality_config['has_type_checking'] = True
                            quality_config['type_checking_tools'].append('mypy')

                        if 'flake8' in tools:
                            quality_config['has_linting'] = True
                            quality_config['linting_tools'].append('flake8')

                except Exception:
                    pass

        except Exception as e:
            logger.error(f"分析代码质量配置失败: {e}")

        return quality_config

    async def _execute_automated_fix(self, fix_decision, project_path: str, session_id: str) -> Dict[str, Any]:
        """执行自动化修复"""
        try:
            logger.info("🤖 执行自动化修复策略...")

            start_time = time.time()
            commands = fix_decision.automated_commands or []

            if not commands:
                return {
                    'success': False,
                    'message': '没有可执行的自动化命令',
                    'strategy': 'automated',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': 0
                }

            # 执行自动化命令
            results = []
            for i, command in enumerate(commands):
                logger.info(f"🔧 执行命令 {i+1}/{len(commands)}: {command}")

                result = await self._execute_command(command, project_path)
                results.append(result)

                # 记录每个命令的执行
                global_conversation_logger.log_round(
                    session_id=session_id,
                    round_name=f"自动化修复命令 {i+1}",
                    prompt=f"执行命令: {command}",
                    response=f"执行结果: {'成功' if result['success'] else '失败'}\n输出: {result.get('output', '')}",
                    model_name="automated-executor",
                    duration=result.get('duration', 0),
                    status=ConversationStatus.SUCCESS if result['success'] else ConversationStatus.FAILED
                )

            execution_time = time.time() - start_time
            successful_commands = sum(1 for r in results if r['success'])
            success_rate = successful_commands / len(commands) if commands else 0

            # 如果自动化修复失败，立即切换到Aider（降低阈值，提高Aider介入频率）
            if success_rate < 0.8:  # 从0.5提高到0.8，更容易触发Aider介入
                logger.warning(f"自动化修复成功率不理想 ({success_rate:.1%})，自动切换到Aider执行器...")

                # 构建失败上下文
                failure_context = {
                    'failed_commands': [r for r in results if not r['success']],
                    'original_error_analysis': fix_decision,
                    'automation_failure_reason': f'自动化命令执行失败，成功率仅 {success_rate:.1%}',
                    'command_outputs': [r.get('output', '') for r in results],
                    'command_errors': [r.get('error', '') for r in results if not r['success']]
                }

                # 切换到Aider执行器
                aider_result = await self._execute_aider_fix_with_context(
                    fix_decision, project_path, session_id, failure_context
                )

                return {
                    'success': aider_result['success'],
                    'message': f'自动化修复失败，已切换到Aider: {aider_result["message"]}',
                    'strategy': 'automated_fallback_to_aider',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time + aider_result.get('execution_time', 0),
                    'automation_results': results,
                    'aider_result': aider_result,
                    'success_rate': success_rate
                }

            return {
                'success': successful_commands > 0,
                'message': f'自动化修复执行了 {len(commands)} 个命令，成功 {successful_commands} 个',
                'strategy': 'automated',
                'complexity': fix_decision.complexity.value,
                'execution_time': execution_time,
                'command_results': results,
                'success_rate': success_rate
            }

        except Exception as e:
            logger.error(f"自动化修复执行失败: {e}")
            return {
                'success': False,
                'message': f'自动化修复失败: {str(e)}',
                'strategy': 'automated',
                'complexity': fix_decision.complexity.value,
                'execution_time': 0
            }

    async def _execute_aider_fix_with_context(self, fix_decision, project_path: str, session_id: str, failure_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行带失败上下文的Aider修复"""
        try:
            logger.info("🧠 执行Aider AI修复策略（带自动化失败上下文）...")

            start_time = time.time()

            # 构建增强的Aider执行计划，包含失败上下文
            enhanced_instructions = f"""
## 🎯 修复任务说明（自动化修复失败后的Aider接管）

### 🚨 自动化修复失败情况
{failure_context.get('automation_failure_reason', '自动化修复失败')}

### 💥 失败的自动化命令
{chr(10).join([f"- {cmd['command']}: {cmd.get('error', '执行失败')}" for cmd in failure_context.get('failed_commands', [])])}

### 🎯 原始问题分析
{fix_decision.aider_instructions or '需要AI深度分析和修复'}

### 🔧 修复要求
1. 分析自动化修复失败的原因
2. 理解原始问题的本质
3. 提供更智能的解决方案
4. 确保修复的完整性和正确性
5. 避免简单的命令执行，使用AI理解和代码生成

请用中文详细说明你的修复思路，然后实施修复。
"""

            execution_plan = {
                'task_type': 'intelligent_fix_with_context',
                'complexity': fix_decision.complexity.value,
                'instructions': enhanced_instructions,
                'project_path': project_path,
                'failure_context': failure_context,
                'steps': [
                    {
                        'step_id': 1,
                        'description': f'AI智能修复（自动化失败后接管） - {fix_decision.complexity.value}级别',
                        'target': 'code_fix_with_context',
                        'instructions': enhanced_instructions
                    }
                ]
            }

            # 使用Aider执行器
            if self.aider_executor.aider_available:
                result = await self.aider_executor.execute_plan(execution_plan, session_id)

                execution_time = time.time() - start_time

                return {
                    'success': result.success,
                    'message': f'Aider接管修复完成: {result.message}',
                    'strategy': 'aider_guided_with_context',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'aider_result': {
                        'executed_steps': len(result.executed_steps),
                        'failed_steps': len(result.failed_steps),
                        'total_time': result.execution_time
                    },
                    'context_provided': True
                }
            else:
                logger.error("Aider不可用，无法进行智能修复")
                return {
                    'success': False,
                    'message': 'Aider不可用，无法进行智能修复',
                    'strategy': 'aider_unavailable',
                    'execution_time': 0
                }

        except Exception as e:
            logger.error(f"带上下文的Aider修复执行失败: {e}")
            return {
                'success': False,
                'message': f'带上下文的Aider修复失败: {str(e)}',
                'strategy': 'aider_guided_with_context',
                'execution_time': 0
            }

    async def _execute_aider_fix(self, fix_decision, project_path: str, session_id: str, error_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """执行Aider修复"""
        try:
            logger.info("🧠 执行Aider AI修复策略...")

            start_time = time.time()

            # 构建Aider执行计划
            execution_plan = {
                'task_type': 'intelligent_fix',
                'complexity': fix_decision.complexity.value,
                'instructions': fix_decision.aider_instructions,
                'project_path': project_path,
                'steps': [
                    {
                        'step_id': 1,
                        'description': f'AI智能修复 - {fix_decision.complexity.value}级别',
                        'target': 'code_fix',
                        'instructions': fix_decision.aider_instructions
                    }
                ]
            }

            # 使用Aider执行器
            if self.aider_executor.aider_available:
                result = await self.aider_executor.execute_plan(execution_plan, session_id)

                execution_time = time.time() - start_time

                return {
                    'success': result.success,
                    'message': f'Aider修复完成: {result.message}',
                    'strategy': 'aider_guided',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'aider_result': {
                        'executed_steps': len(result.executed_steps),
                        'failed_steps': len(result.failed_steps),
                        'total_time': result.execution_time
                    }
                }
            else:
                # Aider不可用，回退到自动化策略
                logger.warning("Aider不可用，回退到自动化修复")
                return await self._execute_automated_fix(fix_decision, project_path, session_id)

        except Exception as e:
            logger.error(f"Aider修复执行失败: {e}")
            return {
                'success': False,
                'message': f'Aider修复失败: {str(e)}',
                'strategy': 'aider_guided',
                'complexity': fix_decision.complexity.value,
                'execution_time': 0
            }

    async def _execute_hybrid_fix(self, fix_decision, project_path: str, session_id: str, error_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """执行混合修复策略"""
        try:
            logger.info("🔄 执行混合修复策略...")

            start_time = time.time()

            # 先执行自动化修复
            auto_result = await self._execute_automated_fix(fix_decision, project_path, session_id)

            # 如果自动化修复不完全成功，使用Aider补充（降低阈值，更容易触发）
            if not auto_result['success'] or auto_result.get('success_rate', 0) < 0.9:
                logger.info("自动化修复不完全成功，启动Aider补充修复...")
                aider_result = await self._execute_aider_fix(fix_decision, project_path, session_id, error_analysis)

                execution_time = time.time() - start_time

                return {
                    'success': auto_result['success'] or aider_result['success'],
                    'message': f'混合修复完成 - 自动化: {auto_result["message"]}, Aider: {aider_result["message"]}',
                    'strategy': 'hybrid',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'auto_result': auto_result,
                    'aider_result': aider_result
                }
            else:
                execution_time = time.time() - start_time
                return {
                    'success': True,
                    'message': '自动化修复已成功，无需Aider补充',
                    'strategy': 'hybrid_auto_only',
                    'complexity': fix_decision.complexity.value,
                    'execution_time': execution_time,
                    'auto_result': auto_result
                }

        except Exception as e:
            logger.error(f"混合修复执行失败: {e}")
            return {
                'success': False,
                'message': f'混合修复失败: {str(e)}',
                'strategy': 'hybrid',
                'complexity': fix_decision.complexity.value,
                'execution_time': 0
            }

    async def _execute_command(self, command: str, project_path: str) -> Dict[str, Any]:
        """执行单个命令"""
        try:
            import subprocess
            import time
            import platform

            start_time = time.time()

            logger.info(f"🔧 执行命令: {command}")
            logger.info(f"📁 工作目录: {project_path}")

            # 根据操作系统选择执行方式
            if platform.system().lower() == 'windows':
                # Windows使用PowerShell，处理复杂命令
                if '"' in command or "'" in command or '|' in command:
                    # 对于复杂命令，使用Base64编码
                    import base64
                    encoded_command = base64.b64encode(command.encode('utf-16le')).decode('ascii')
                    result = subprocess.run(
                        ["powershell", "-EncodedCommand", encoded_command],
                        cwd=project_path,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                else:
                    # 简单命令直接执行
                    result = subprocess.run(
                        ["powershell", "-Command", command],
                        cwd=project_path,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
            else:
                # Linux/Mac使用bash
                result = subprocess.run(
                    command,
                    cwd=project_path,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    shell=True
                )

            duration = time.time() - start_time

            # 详细日志记录
            logger.info(f"⏱️ 命令执行时间: {duration:.2f}秒")
            logger.info(f"🔢 返回码: {result.returncode}")

            if result.stdout:
                logger.info(f"📤 标准输出: {result.stdout[:300]}...")
            if result.stderr:
                logger.warning(f"⚠️ 错误输出: {result.stderr[:300]}...")

            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode,
                'duration': duration,
                'command': command
            }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'output': '',
                'error': '命令执行超时',
                'return_code': -1,
                'duration': 60,
                'command': command
            }
        except Exception as e:
            return {
                'success': False,
                'output': '',
                'error': str(e),
                'return_code': -1,
                'duration': 0,
                'command': command
            }

    def _format_execution_plan(self, fix_decision) -> str:
        """格式化执行计划"""
        if fix_decision.strategy == FixStrategy.AUTOMATED:
            commands = fix_decision.automated_commands or []
            return f"将执行 {len(commands)} 个自动化命令:\n" + "\n".join(f"- {cmd}" for cmd in commands)
        elif fix_decision.strategy == FixStrategy.AIDER_GUIDED:
            return "将使用Aider AI进行智能代码修复"
        else:
            return "将使用混合策略：先自动化修复，必要时使用Aider补充"


# 全局实例
global_intelligent_fix_coordinator = IntelligentFixCoordinator()
