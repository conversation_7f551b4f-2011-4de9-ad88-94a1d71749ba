2025-05-30 17:23:36,276 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-30 17:23:36,276 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-30 17:23:36,276 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-30 17:23:36,277 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-30 17:23:36,278 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-30 17:23:36,281 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:23:36,282 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:23:36,431 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:23:36,431 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-30 17:23:36,432 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:136 - _process_with_aider - 使用AiderBasedTaskExecutor处理任务: 作业失败分析 - lint (Job 978)
2025-05-30 17:23:36,432 - bot_agent.engines.simple_progress_tracker - WARNING - simple_progress_tracker.py:50 - send_progress_update - 缺少项目ID或Issue IID，跳过进度更新
2025-05-30 17:23:36,433 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-30 17:23:36,433 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:23:36,433 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:23:36,824 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:23:36,824 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-30 17:23:36,825 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-30 17:23:36,825 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-30 17:23:36,826 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-30 17:23:36,826 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-30 17:23:36,826 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-30 17:23:36,827 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-30 17:23:36,828 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: information_query
2025-05-30 17:23:36,828 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TerminalTools
2025-05-30 17:23:36,828 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TestingTools
2025-05-30 17:23:36,829 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: LogAnalysisTools
2025-05-30 17:23:36,829 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DatabaseTools
2025-05-30 17:23:36,829 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DependencyTools
2025-05-30 17:23:36,829 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DebugTools
2025-05-30 17:23:36,830 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: FrontendDebugTools
2025-05-30 17:23:36,830 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: RefactorTools
2025-05-30 17:23:36,830 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DocumentationTools
2025-05-30 17:23:36,831 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: SecurityTools
2025-05-30 17:23:36,831 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: CodeGenerationTools
2025-05-30 17:23:36,831 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-30 17:23:36,832 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-30 17:23:36,832 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-30 17:23:36,833 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-30 17:23:36,833 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-30 17:23:36,833 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-30 17:23:36,834 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:174 - _process_with_aider - 使用Aider执行引擎处理 TaskType.BUG_FIX 类型任务
2025-05-30 17:23:36,834 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 551bc32b-f51f-4bf6-afd7-be7fffe634fd
2025-05-30 17:23:36,850 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-30 17:23:36,851 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-30 17:23:36,851 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-30 17:23:36,852 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-30 17:23:40,964 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 17:23:40,965 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-30T03:37:55.996Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-30T04:04:37.351Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-30 17:23:40,966 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:40,967 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-30 17:23:40,967 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:40,972 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-30 17:23:40,972 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 4.14s
2025-05-30 17:23:41,584 - bot_agent.memory.project_memory - INFO - project_memory.py:42 - __init__ - ProjectMemoryManager initialized for project: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:41,597 - bot_agent.memory.global_memory - INFO - global_memory.py:60 - load_memory - Global memory loaded
2025-05-30 17:23:41,598 - bot_agent.memory.memory_integration - INFO - memory_integration.py:78 - prepare_context_for_task - Prepared context for task: 926 characters
2025-05-30 17:23:41,598 - bot_agent.engines.tool_integration - INFO - tool_integration.py:49 - enhance_aider_request - 开始增强Aider请求
2025-05-30 17:23:41,598 - bot_agent.tools.tool_router - INFO - tool_router.py:184 - analyze_task - 分析任务: 
任务类型: TaskType.BUG_FIX
任务标题: 作业失败分析 - lint (Job 978)

任务描述:

## 🚨 作业失败自动分析

**项目**: ai-proxy
**作业名称**: lint
**作业ID**: 978
**Pipeline ID**: 262
**阶段**: test
**分支**: aider-plus-dev
**状态**: failed
**失败原因**: script_failure

### 任务要求
1. 分析作业失败的具体原因
2. 收集作业日志和错误信息
3. 提供针对性的修复建议
4. 如果是部署作业，评估回滚需求

### 自动化指令
请立即分析Job 978的失败原因，收集详细日志，并提供修复方案。


# 全局工作记忆
## 相关工作习惯
- task_preference: 成功处理 TaskType.BUG_FIX 类型任务 (频率: 109)
- task_preference: 成功处理 TaskType.PROJECT_ANALYSIS 类型任务 (频率: 4)
- task_preference: 成功处理 OPTIMIZATION 类型任务 (频率: 1)

## 偏好设置
tools: VSCode, PowerShell, GitLab, 2025-05-27T15:23:37.010003, GitLab, aider-plus; programming_languages: Python, JavaScript, Rust, 2025-05-30T12:04:34.194909, go, bug_fix, 1; frameworks: FastAPI, PostgreSQL, Redis, pytest, 2025-05-30T10:14:58.547585, fastapi, 作业失败分析 - test (Job 959), 1


# 项目记忆
## 项目架构
Overview: AI API代理服务; Patterns: 代理模式, 策略模式, 工厂模式; Technologies: FastAPI, asyncio, httpx

## general 编码规范
- 作业失败分析 - lint (Job 653)
- 作业失败分析 - lint (Job 664)
- 作业失败分析 - lint (Job 677)



# 环境信息
## 环境配置
- last_project_path: E:\aider-git-repos\ai-proxy
- last_task_type: bug_fix
- os: Windows 11
- python_version: 3.9
- conda_env: aider-plus
- git_user: aider-worker
- workspace: E:\Projects\aider-plus
- last_used_tool: aider


## 🚨 严格执行指令 - 禁止偏离

### ⚠️ 绝对禁止的行为：
1. **禁止创建任何新文件** - 不允许创建.py、.js、.md等任何新文件
2. **禁止编写代码** - 不允许编写类、函数、测试代码
3. **禁止基于假设分析** - 必须基于真实数据和日志
4. **禁止提供通用建议** - 必须针对具体问题提供具体解决方案
5. **禁止代码审查** - 当前任务不是代码审查，是问题分析

### ✅ 必须执行的步骤（严格按顺序）：

#### 第1步：获取真实数据（必须完成）
- 使用GitLabClient获取Job的实际日志内容
- 如果无法获取，明确说明原因并停止
- 不允许基于"没有日志"进行假设性分析

#### 第2步：分析具体问题（基于真实数据）
- 使用LogAnalysisTools分析实际的错误日志
- 识别具体的错误类型、文件、行号
- 确定失败的根本原因

#### 第3步：执行具体修复（针对性解决）
- 使用TerminalTools执行针对性的修复命令
- 修复具体识别出的问题
- 不执行通用的格式化命令

#### 第4步：验证修复效果
- 使用TestingTools验证修复是否成功
- 确认问题已解决

### 🎯 当前任务要求：
如果这是作业失败分析任务，你必须：
1. 获取指定Job ID的实际失败日志
2. 分析日志中的具体错误信息
3. 提供针对这些具体错误的修复方案
4. 验证修复效果

### 🚫 严格禁止：
- 说"Since we don't have the actual log files"
- 提供black、flake8等通用命令
- 创建LintAnalyzer等新类
- 进行代码审查
- 创建测试文件

现在开始执行，严格遵循上述要求：

2025-05-30 17:23:41,603 - bot_agent.tools.tool_router - INFO - tool_router.py:217 - analyze_task - 生成了 6 个工具建议
2025-05-30 17:23:41,604 - bot_agent.engines.tool_integration - INFO - tool_integration.py:67 - enhance_aider_request - 请求增强完成，添加了 6 个工具建议
2025-05-30 17:23:41,604 - bot_agent.engines.task_executor - INFO - task_executor.py:252 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 17:23:48,405 - bot_agent.engines.task_executor - INFO - task_executor.py:404 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-30 17:23:48,482 - bot_agent.engines.task_executor - INFO - task_executor.py:425 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_provider_security.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_lint_analysis.py', 'requirements.txt', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_error.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_health_check.py', 'tests\\test_proxy_service_error.py', 'api_proxy\\config.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_proxy_service_integration.py', 'setup.py', 'tests\\test_job_analysis_integration.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_provider_initialization.py', 'api_proxy\\__init__.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_failure_analysis_integration.py', 'api_proxy\\models.py']
2025-05-30 17:23:48,486 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 🔍 启用Aider操作监控系统
2025-05-30 17:23:50,109 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:49 - log_operation - [AIDER_MONITOR] ✅ coder_created: 创建监控的Coder
2025-05-30 17:23:50,109 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:53 - log_operation - [AIDER_MONITOR]   kwargs: {'main_model': Model(name='openrouter/deepseek/deepseek-chat-v3-0324:free', edit_format='diff', weak_model_name='openrouter/deepseek/deepseek-chat-v3-0324:free', use_repo_map=True, send_undo_reply=False, lazy=False, overeager=False, reminder='user', examples_as_sys_msg=True, extra_params=None, cache_control=False, caches_by_default=True, use_system_prompt=True, use_temperature=0.3, streaming=True, editor_model_name='openrouter/deepseek/deepseek-r1:free', editor_edit_format='editor-diff', reasoning_tag=None, remove_reasoning=None, system_prompt_prefix=None, accepts_settings=[]), 'io': <bot_agent.aider_extensions.aider_monitor.MonitoredIO object at 0x000002762ABCC770>, 'repo': <aider.repo.GitRepo object at 0x000002762A9C1A00>, 'fnames': ['tests\\test_provider_security.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_lint_analysis.py', 'requirements.txt', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_error.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_health_check.py', 'tests\\test_proxy_service_error.py', 'api_proxy\\config.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_proxy_service_integration.py', 'setup.py', 'tests\\test_job_analysis_integration.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_provider_initialization.py', 'api_proxy\\__init__.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_failure_analysis_integration.py', 'api_proxy\\models.py'], 'use_git': False, 'auto_commits': False, 'dirty_commits': False, 'auto_lint': False, 'auto_test': False, 'stream': False, 'verbose': False, 'chat_language': 'Chinese', 'suggest_shell_commands': False, 'auto_accept_architect': True, 'map_tokens': 0, 'cache_prompts': False, 'num_cache_warming_pings': 0}
2025-05-30 17:23:50,110 - bot_agent.engines.task_executor - INFO - task_executor.py:302 - _execute_with_aider - 使用优化的Aider执行任务: 作业失败分析 - lint (Job 978)
2025-05-30 17:23:50,112 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-30 17:23:50,112 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-30 17:23:50,112 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-30 17:23:50,113 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-30 17:23:51,433 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 17:23:51,434 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-30T03:37:55.996Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-30T04:04:37.351Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-30 17:23:51,436 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:51,436 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-30 17:23:51,436 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:51,437 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-30 17:23:51,437 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 1.33s
2025-05-30 17:23:51,438 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: task_1748597031_1748597031
2025-05-30 17:23:51,438 - bot_agent.engines.task_executor - INFO - task_executor.py:773 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-30 17:23:51,439 - bot_agent.engines.task_executor - INFO - task_executor.py:777 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-30 17:23:51,441 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: job_failure_analysis
2025-05-30 17:23:51,441 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始作业失败分析
2025-05-30 17:23:51,442 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 提取Job ID
2025-05-30 17:23:51,443 - bot_agent.engines.task_executor - INFO - task_executor.py:1063 - _handle_job_failure_analysis - 提取到Job ID: 978
2025-05-30 17:23:51,443 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 成功提取Job ID: 978
2025-05-30 17:23:51,443 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取项目ID
2025-05-30 17:23:51,444 - bot_agent.engines.task_executor - INFO - task_executor.py:1071 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-30 17:23:51,444 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取到项目ID: 9
2025-05-30 17:23:51,445 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 准备工作空间
2025-05-30 17:23:51,445 - bot_agent.engines.task_executor - INFO - task_executor.py:1086 - _handle_job_failure_analysis - 作业失败分析使用项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:51,446 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:51,446 - bot_agent.engines.task_executor - INFO - task_executor.py:1092 - _handle_job_failure_analysis - 已更新会话 task_1748597031_1748597031 的项目路径为: E:\aider-git-repos\ai-proxy
2025-05-30 17:23:51,447 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 会话路径已更新
2025-05-30 17:23:51,447 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始智能分析
2025-05-30 17:23:51,447 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 预执行数据收集
2025-05-30 17:23:51,455 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:23:51,455 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:23:51,649 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:23:51,650 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-30 17:23:51,751 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:70 - get_job_info_and_log - 🔍 获取Job 978的信息和日志...
2025-05-30 17:23:51,751 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:437 - get_job - Getting job 978 in project 9
2025-05-30 17:23:51,751 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9/jobs/978
2025-05-30 17:23:52,555 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 17:23:52,556 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 978, 'status': 'failed', 'stage': 'test', 'name': 'lint', 'ref': 'aider-plus-dev', 'tag': False, 'coverage': None, 'allow_failure': False, 'created_at': '2025-05-30T09:20:33.222Z', 'started_at': '2025-05-30T09:21:34.681Z', 'finished_at': '2025-05-30T09:22:51.885Z', 'erased_at': None, 'duration': 77.203975, 'queued_duration': 59.578389, 'user': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer', 'created_at': '2025-04-27T02:44:28.626Z', 'bio': '', 'location': '', 'public_email': None, 'skype': '', 'linkedin': '', 'twitter': '', 'discord': '', 'website_url': '', 'organization': '', 'job_title': '', 'pronouns': None, 'bot': False, 'work_information': None, 'followers': 0, 'following': 0, 'local_time': None}, 'commit': {'id': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'short_id': '276da7d1', 'created_at': '2025-05-30T12:04:29.000+08:00', 'parent_ids': ['1c0620e71689773024648a0a20598166e9f0ec02'], 'title': 'AI自动修改: 作业失败分析 - lint (Job 973)', 'message': 'AI自动修改: 作业失败分析 - lint (Job 973)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'authored_date': '2025-05-30T12:04:29.000+08:00', 'committer_name': 'Longer (aider)', 'committer_email': '<EMAIL>', 'committed_date': '2025-05-30T12:04:29.000+08:00', 'trailers': {}, 'extended_trailers': {}, 'web_url': 'http://***************/Longer/ai-proxy/-/commit/276da7d1d761c8fb48ad935f77b22d9b2105fdaf'}, 'pipeline': {'id': 262, 'iid': 84, 'project_id': 9, 'sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'ref': 'aider-plus-dev', 'status': 'failed', 'source': 'push', 'created_at': '2025-05-30T04:04:38.103Z', 'updated_at': '2025-05-30T09:22:53.455Z', 'web_url': 'http://***************/Longer/ai-proxy/-/pipelines/262'}, 'failure_reason': 'script_failure', 'web_url': 'http://***************/Longer/ai-proxy/-/jobs/978', 'project': {'ci_job_token_scope_enabled': False}, 'artifacts': [], 'runner': {'id': 1, 'description': 'docker-runner-137', 'ip_address': None, 'active': True, 'paused': False, 'is_shared': True, 'runner_type': 'instance_type', 'name': None, 'online': True, 'status': 'online'}, 'runner_manager': {'id': 2, 'system_id': 'r_zhwE8WyyfNZL', 'version': '17.11.0', 'revision': '0f67ff19', 'platform': 'linux', 'architecture': 'amd64', 'created_at': '2025-04-27T09:00:17.697Z', 'contacted_at': '2025-05-30T09:22:52.896Z', 'ip_address': '***************', 'status': 'online'}, 'artifacts_expire_at': None, 'archived': False, 'tag_list': []}
2025-05-30 17:23:52,557 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:444 - get_job - 成功获取作业信息: Job 978 - lint (failed)
2025-05-30 17:23:52,557 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:466 - get_job_log - Getting log for job 978 in project 9
2025-05-30 17:23:53,144 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:484 - get_job_log - 成功获取作业日志: Job 978, 长度: 6610 字符
2025-05-30 17:23:53,145 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-30 17:23:53,146 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:535 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-30 17:23:53,148 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:262 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfdv2fgx4.log']
2025-05-30 17:23:53,173 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:550 - analyze_job_log - ✅ 作业日志分析完成
2025-05-30 17:23:53,175 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 调用AI进行智能分析
2025-05-30 17:23:53,175 - bot_agent.engines.task_executor - INFO - task_executor.py:1198 - _handle_job_failure_analysis - 🧠 开始使用Bot推理分析器进行作业失败分析...
2025-05-30 17:23:53,179 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:44 - __init__ - BotReasoningAnalyzer initialized with model: deepseek/deepseek-r1:free
2025-05-30 17:23:53,179 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:60 - analyze_job_failure - 🧠 开始Bot级别推理分析...
2025-05-30 17:23:53,829 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:155 - _call_reasoning_model - 🧠 调用推理模型: deepseek/deepseek-r1:free
2025-05-30 17:24:28,314 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:175 - _call_reasoning_model - 🧠 推理模型响应长度: 1033 字符
2025-05-30 17:24:28,314 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - Bot推理分析完成，置信度: 0.95
2025-05-30 17:24:28,321 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 启动智能修复协调器
2025-05-30 17:24:28,321 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 执行智能修复路由分析
2025-05-30 17:24:28,329 - bot_agent.executors.aider_executor - INFO - aider_executor.py:57 - _check_aider_availability - Aider模块可用
2025-05-30 17:24:28,329 - bot_agent.executors.aider_executor - INFO - aider_executor.py:44 - __init__ - AiderExecutor initialized
2025-05-30 17:24:28,331 - bot_agent.executors.aider_executor - INFO - aider_executor.py:57 - _check_aider_availability - Aider模块可用
2025-05-30 17:24:28,332 - bot_agent.executors.aider_executor - INFO - aider_executor.py:44 - __init__ - AiderExecutor initialized
2025-05-30 17:24:28,332 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:47 - execute_intelligent_fix - 🚀 启动智能修复协调器...
2025-05-30 17:24:28,496 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:342 - _collect_git_info - 收集Git信息失败: 'NoneType' object has no attribute 'strip'
2025-05-30 17:24:28,497 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:223 - cached_operation - 💾 缓存存储: gather_project_context
2025-05-30 17:24:28,497 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:258 - controlled_ai_call - 🤖 开始AI调用: intelligent_fix_routing
2025-05-30 17:24:28,497 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:99 - analyze_and_route - 🧠 开始智能修复路由分析...
2025-05-30 17:24:28,501 - bot_agent.tools.error_classifier - INFO - error_classifier.py:91 - classify_errors - ✅ 成功分类 3 个错误
2025-05-30 17:24:28,502 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:110 - analyze_and_route - 📊 错误分类完成: 3 个错误
2025-05-30 17:24:28,503 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:264 - _deep_reasoning_analysis - 🚀 启动增强LLM调用...
2025-05-30 17:24:28,503 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:228 - call_reasoning_ai - 🔥 开始调用DeepSeek R1推理模型进行智能路由分析...
2025-05-30 17:24:29,058 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:240 - call_reasoning_ai - 📤 发送请求到OpenRouter API: deepseek/deepseek-r1:free
2025-05-30 17:24:29,071 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:24:29,072 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:24:29,072 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:24:29,072 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'daaafc1a-8889-43af-8c57-9b457f2887d3', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '4eed34d2-a26f-45ab-b5d4-be3552aac978', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '431f9dd6-81b0-47e5-8cfb-186d1dd4ddec', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3465'}
2025-05-30 17:24:29,073 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:24:29,073 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:24:29,073 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:24:29,073 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:24:29,074 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'daaafc1a-8889-43af-8c57-9b457f2887d3', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '4eed34d2-a26f-45ab-b5d4-be3552aac978', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '431f9dd6-81b0-47e5-8cfb-186d1dd4ddec', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3465'}
2025-05-30 17:24:29,074 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 262, 'iid': 84, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'before_sha': '1c0620e71689773024648a0a20598166e9f0ec02', 'source': 'push', 'status': 'failed', 'detailed_status': 'failed', 'stages': ['test', 'build'], 'created_at': '2025-05-30 04:04:38 UTC', 'finished_at': '2025-05-30 09:22:53 UTC', 'duration': 203, 'queued_duration': 8, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/262'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'message': 'AI自动修改: 作业失败分析 - lint (Job 973)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 973)', 'timestamp': '2025-05-30T12:04:29+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 975, 'stage': 'test', 'name': 'test', 'status': 'failed', 'created_at': '2025-05-30 04:04:38 UTC', 'started_at': '2025-05-30 04:04:43 UTC', 'finished_at': '2025-05-30 04:06:49 UTC', 'duration': 125.879277, 'queued_duration': 2.906693, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 977, 'stage': 'build', 'name': 'build', 'status': 'skipped', 'created_at': '2025-05-30 04:04:38 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 978, 'stage': 'test', 'name': 'lint', 'status': 'failed', 'created_at': '2025-05-30 09:20:33 UTC', 'started_at': '2025-05-30 09:21:34 UTC', 'finished_at': '2025-05-30 09:22:51 UTC', 'duration': 77.203975, 'queued_duration': 59.578389, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-30 17:24:29,076 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-30 17:24:29,076 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-30 17:24:29,076 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 262 for aider-plus-dev is failed (Project: ai-proxy, User: Longer)
2025-05-30 17:24:29,077 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 262 status failed recorded (no AI monitoring needed)
2025-05-30 17:24:29,077 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 262 status failed recorded'}
2025-05-30 17:24:48,842 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:251 - call_reasoning_ai - 📥 收到API响应: status=200
2025-05-30 17:24:48,843 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:256 - call_reasoning_ai - ✅ DeepSeek R1推理完成，响应长度: 1044 字符
2025-05-30 17:24:48,845 - bot_agent.tools.intelligent_fix_router - WARNING - intelligent_fix_router.py:277 - _deep_reasoning_analysis - 推理模型响应JSON解析失败，使用文本分析
2025-05-30 17:24:48,845 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:141 - analyze_and_route - 🎯 修复决策: automated (复杂度: simple, 置信度: 0.60)
2025-05-30 17:24:48,845 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:266 - controlled_ai_call - ✅ AI调用完成: intelligent_fix_routing
2025-05-30 17:24:48,848 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:398 - _execute_automated_fix - 🤖 执行自动化修复策略...
2025-05-30 17:24:48,848 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:415 - _execute_automated_fix - 🔧 执行命令 1/1: powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-30 17:24:48,848 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:679 - _execute_command - 🔧 执行命令: powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace '#.*', '' | Where-Object {$_.trim() -ne ''} | Set-Content .flake8"
2025-05-30 17:24:48,849 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:680 - _execute_command - 📁 工作目录: E:\aider-git-repos\ai-proxy
2025-05-30 17:24:53,086 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:719 - _execute_command - ⏱️ 命令执行时间: 4.24秒
2025-05-30 17:24:53,087 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:720 - _execute_command - 🔢 返回码: 1
2025-05-30 17:24:53,087 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:725 - _execute_command - ⚠️ 错误输出: #< CLIXML
所在位置 行:1 字符: 94
+ ... n SilentlyContinue) -replace '#.*', '' | Where-Object {.trim() -ne '' ...
+                                                                  ~
“(”后面应为表达式。
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId...
2025-05-30 17:24:53,090 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:437 - _execute_automated_fix - 自动化修复成功率不理想 (0.0%)，自动切换到Aider执行器...
2025-05-30 17:24:53,090 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:487 - _execute_aider_fix_with_context - 🧠 执行Aider AI修复策略（带自动化失败上下文）...
2025-05-30 17:24:53,090 - bot_agent.executors.aider_executor - INFO - aider_executor.py:88 - execute_plan - 🚀 开始执行计划: unknown
2025-05-30 17:24:53,091 - bot_agent.executors.aider_executor - INFO - aider_executor.py:146 - _create_aider_instance - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 17:24:53,112 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:62 - validate_fix - 🔍 开始智能修复验证...
2025-05-30 17:24:53,113 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:134 - _validate_original_commands - 🔧 验证命令: flake8 --config .flake8
2025-05-30 17:24:53,113 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:134 - _validate_original_commands - 🔧 验证命令: black --check --config pyproject.toml .
2025-05-30 17:24:53,113 - bot_agent.tools.intelligent_fix_validator - INFO - intelligent_fix_validator.py:103 - validate_fix - ✅ 修复验证完成: failed (成功率: 0.0%)
2025-05-30 17:24:53,124 - bot_agent.tools.fix_learning_engine - INFO - fix_learning_engine.py:50 - load_learning_data - ✅ 加载了 2 个修复模式
2025-05-30 17:24:53,125 - bot_agent.tools.fix_learning_engine - INFO - fix_learning_engine.py:64 - save_learning_data - 💾 保存了 2 个修复模式
2025-05-30 17:24:53,125 - bot_agent.tools.fix_learning_engine - INFO - fix_learning_engine.py:169 - record_fix_attempt - 📚 记录修复尝试: job:lint|types:flake8_config_error,generic_job_failure|keywords:extend-ignore -> automated_fallback_to_aider (成功)
2025-05-30 17:24:53,126 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1703 - verify_fixes - ✅ 开始验证修复效果...
2025-05-30 17:24:55,962 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:164 - execute_command - 命令执行完成: powershell -Command "black --check --config pyproject.toml ."
2025-05-30 17:24:55,962 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:165 - execute_command - 返回码: 0, 成功: True
2025-05-30 17:24:55,962 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:166 - execute_command - 执行时间: 2.83秒
2025-05-30 17:24:55,963 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check --config pyproject.toml ."
2025-05-30 17:24:58,583 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:164 - execute_command - 命令执行完成: powershell -Command "flake8 ."
2025-05-30 17:24:58,583 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:165 - execute_command - 返回码: 1, 成功: False
2025-05-30 17:24:58,583 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:166 - execute_command - 执行时间: 2.62秒
2025-05-30 17:24:58,583 - bot_agent.tools.terminal_tools - WARNING - terminal_tools.py:171 - execute_command - 错误输出: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\s...
2025-05-30 17:24:58,584 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-30 17:24:58,585 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-30 17:25:01,206 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:164 - execute_command - 命令执行完成: powershell -EncodedCommand cAB5AHQAaABvAG4AIAAtAG0AIABwAHkAXwBjAG8AbQBwAGkAbABlACAAIgBlAHgAYQBtAHAAbABlAC4AcAB5ACIAIAAiAHMAZQB0AHUAcAAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGMAbwBuAGYAaQBnAC4AcAB5ACIAIAAiAGEAcABpAF8AcAByAG8AeAB5AFwAaABlAGEAbAB0AGgAXwBjAGgAZQBjAGsALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABqAG8AYgBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AZgBhAGkAbAB1AHIAZQBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAHMAZQByAHYAaQBjAGUALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABtAG8AZABlAGwAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAG0AbwBuAGkAdABvAHIAaQBuAGcALgBwAHkAIgA=
2025-05-30 17:25:01,207 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:165 - execute_command - 返回码: 0, 成功: True
2025-05-30 17:25:01,207 - bot_agent.tools.terminal_tools - INFO - terminal_tools.py:166 - execute_command - 执行时间: 2.62秒
2025-05-30 17:25:01,208 - bot_agent.tools.terminal_tools - WARNING - terminal_tools.py:171 - execute_command - 错误输出: #< CLIXML
<Objs Version="1.1.0.1" xmlns="http://schemas.microsoft.com/powershell/2004/04"><Obj S="progress" RefId="0"><TN RefId="0"><T>System.Management.Automation.PSCustomObject</T><T>System.Object</T></TN><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</PI><PC>-1</PC><T>Completed</T><SR>-1</SR><SD> </SD></PR></MS></Obj><Obj S="progress" RefId="1"><TNRef RefId="0" /><MS><I64 N="SourceId">1</I64><PR N="Record"><AV>正在准备首次使用模块。</AV><AI>0</AI><Nil /><PI>-1</P...
2025-05-30 17:25:01,208 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -EncodedCommand cAB5AHQAaABvAG4AIAAtAG0AIABwAHkAXwBjAG8AbQBwAGkAbABlACAAIgBlAHgAYQBtAHAAbABlAC4AcAB5ACIAIAAiAHMAZQB0AHUAcAAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGMAbwBuAGYAaQBnAC4AcAB5ACIAIAAiAGEAcABpAF8AcAByAG8AeAB5AFwAaABlAGEAbAB0AGgAXwBjAGgAZQBjAGsALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABqAG8AYgBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AZgBhAGkAbAB1AHIAZQBfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAGEAbgBhAGwAeQBzAGkAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAGoAbwBiAF8AbABpAG4AdABfAHMAZQByAHYAaQBjAGUALgBwAHkAIgAgACIAYQBwAGkAXwBwAHIAbwB4AHkAXABtAG8AZABlAGwAcwAuAHAAeQAiACAAIgBhAHAAaQBfAHAAcgBvAHgAeQBcAG0AbwBuAGkAdABvAHIAaQBuAGcALgBwAHkAIgA=
2025-05-30 17:25:01,209 - bot_agent.config.model_config - INFO - model_config.py:141 - log_current_config - 当前AI模型配置:
2025-05-30 17:25:01,210 - bot_agent.config.model_config - INFO - model_config.py:142 - log_current_config -   - 推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-30 17:25:01,210 - bot_agent.config.model_config - INFO - model_config.py:143 - log_current_config -   - 代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 17:25:01,210 - bot_agent.config.model_config - INFO - model_config.py:144 - log_current_config -   - 通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-30 17:25:01,211 - bot_agent.config.model_config - INFO - model_config.py:145 - log_current_config -   - API密钥已设置: 是
2025-05-30 17:25:01,211 - bot_agent.config.model_config - INFO - model_config.py:135 - validate_config - 模型配置验证结果: {'api_key': True, 'analysis_model': True, 'code_generation_model': True}
2025-05-30 17:25:01,212 - bot_agent.config.model_config - INFO - model_config.py:150 - log_current_config - ✅ 所有模型配置正常
2025-05-30 17:25:01,212 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:48 - __init__ - AI Task analyzer initialized with analysis model: deepseek/deepseek-r1:free
2025-05-30 17:25:18,667 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:181 - _ai_analyze_task - AI分析响应: ```json
{
  "task_type": "project_analysis",
  "confidence": 0.9,
  "priority": "high",
  "complexity": "low",
  "reasoning": "任务核心是对修复效果的验证分析而非直接执行修复，属于项目质量评估范畴。虽然涉及bug_fix相关上下文，但主要行为是结果诊断而非代码修改。验证成功率仅50%表明存在未解决问题，需要优先关注。",
  "risks": [
    {
      "type": "residual_defects",
      "level": "high",
      "description": "50%的失败率表明修复不彻底，可能导致系统稳定性或功能完整性问题"
    },
    {
      "type": "validation_gaps",
      "level": "medium",
      "description": "未明确说明验证项的具体标准，可能存在测试覆盖率不足的风险"
    }
  ]
}
```
2025-05-30 17:25:18,668 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:197 - _ai_analyze_task - AI任务分析成功: {'task_type': 'project_analysis', 'confidence': 0.9, 'priority': 'high', 'complexity': 'low', 'reasoning': '任务核心是对修复效果的验证分析而非直接执行修复，属于项目质量评估范畴。虽然涉及bug_fix相关上下文，但主要行为是结果诊断而非代码修改。验证成功率仅50%表明存在未解决问题，需要优先关注。', 'risks': [{'type': 'residual_defects', 'level': 'high', 'description': '50%的失败率表明修复不彻底，可能导致系统稳定性或功能完整性问题'}, {'type': 'validation_gaps', 'level': 'medium', 'description': '未明确说明验证项的具体标准，可能存在测试覆盖率不足的风险'}]}
2025-05-30 17:25:18,671 - bot_agent.engines.task_executor - INFO - task_executor.py:1433 - _handle_job_failure_analysis - ✅ 智能修复和验证流程完成
2025-05-30 17:25:18,671 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - AI智能分析完成
2025-05-30 17:25:18,671 - bot_agent.engines.task_executor - INFO - task_executor.py:1489 - _handle_job_failure_analysis - ✅ AI智能分析完成，保留原始AI响应
2025-05-30 17:25:18,674 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:277 - end_session - Ended conversation session: task_1748597031_1748597031, status: ConversationStatus.SUCCESS
2025-05-30 17:25:18,674 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: job_failure_analysis，执行时间: 87.24s
2025-05-30 17:25:19,193 - bot_agent.engines.task_executor - INFO - task_executor.py:316 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 978)
2025-05-30 17:25:25,165 - bot_agent.engines.task_executor - INFO - task_executor.py:342 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-30 17:25:25,167 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:62 - analyze_task_completion - Analyzed task completion: 6 global, 1 project memories
2025-05-30 17:25:25,171 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: task_preference
2025-05-30 17:25:25,190 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: tool_preference
2025-05-30 17:25:25,193 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: file_creation
2025-05-30 17:25:25,213 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: directory_structure
2025-05-30 17:25:25,217 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-30 17:25:25,234 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-30 17:25:25,238 - bot_agent.memory.project_memory - INFO - project_memory.py:94 - save_coding_standard - Coding standard saved: general
2025-05-30 17:25:25,240 - bot_agent.memory.global_memory - INFO - global_memory.py:150 - save_environment_info - Environment info saved
2025-05-30 17:25:25,241 - bot_agent.memory.memory_integration - INFO - memory_integration.py:111 - learn_from_task_completion - Learned from task completion: 6 global, 1 project memories
2025-05-30 17:25:25,241 - bot_agent.handlers.ai_response_handler - WARNING - ai_response_handler.py:104 - _handle_gitlab_response - Unsupported event type: Job Hook
2025-05-30 17:25:25,243 - bot_agent.dispatcher.router - INFO - router.py:111 - _dispatch_to_component - Task 551bc32b-f51f-4bf6-afd7-be7fffe634fd processed by AI processor: success
2025-05-30 17:25:25,243 - bot_agent.webhook.gitlab - INFO - gitlab.py:1237 - handle_job_event - Job event task routed: {'task_id': '551bc32b-f51f-4bf6-afd7-be7fffe634fd', 'target_component': 'aider', 'status': 'accepted', 'message': 'Task 551bc32b-f51f-4bf6-afd7-be7fffe634fd accepted and processed'}
2025-05-30 17:25:25,243 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'success', 'message': 'Job lint (failed) routed to AI for critical_job_failure', 'task_id': '551bc32b-f51f-4bf6-afd7-be7fffe634fd', 'processing_reason': 'critical_job_failure'}
2025-05-30 17:25:31,580 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:31,580 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:31,582 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:31,582 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'b6d44a2d-f684-4f8f-8f3b-d754947bec3a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '93613f53-bc87-4b99-9674-9b7e01f1513d', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'deb289ae-c95d-48f9-95a9-16bafe2eab1e', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 17:25:31,582 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:31,583 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:31,583 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:31,584 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:31,584 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'b6d44a2d-f684-4f8f-8f3b-d754947bec3a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '93613f53-bc87-4b99-9674-9b7e01f1513d', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'deb289ae-c95d-48f9-95a9-16bafe2eab1e', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 17:25:31,585 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 980, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:25:31,587 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:25:31,587 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:25:31,588 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (980) in stage test is created (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:25:31,588 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status created recorded (no AI processing needed)
2025-05-30 17:25:31,589 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status created recorded'}
2025-05-30 17:25:31,591 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:31,593 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:31,593 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:31,594 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'd7793250-ce37-40ed-886f-d02df552cce4', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '8056f028-3999-44e6-9ac4-4c91fa03f586', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '848f0b05-04ad-47db-bacd-85074dd502a6', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 17:25:31,594 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:31,595 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:31,595 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:31,595 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:31,596 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'd7793250-ce37-40ed-886f-d02df552cce4', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '8056f028-3999-44e6-9ac4-4c91fa03f586', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '848f0b05-04ad-47db-bacd-85074dd502a6', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 17:25:31,597 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 979, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:25:31,598 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:25:31,598 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:25:31,599 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (979) in stage test is created (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:25:31,599 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status created recorded (no AI processing needed)
2025-05-30 17:25:31,600 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status created recorded'}
2025-05-30 17:25:32,049 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:32,049 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:32,049 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:32,050 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '43996ac9-4971-4c56-9666-896d7ddf303d', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '4097d7da-7955-49ce-88a4-75e84a10a7a8', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'd6cb5f4f-b228-434e-85c8-46566d83e759', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-30 17:25:32,050 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:32,051 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:32,051 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:32,051 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:32,052 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '43996ac9-4971-4c56-9666-896d7ddf303d', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '4097d7da-7955-49ce-88a4-75e84a10a7a8', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'd6cb5f4f-b228-434e-85c8-46566d83e759', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-30 17:25:32,052 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 981, 'build_name': 'build', 'build_stage': 'build', 'build_status': 'created', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:25:32,054 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:25:32,055 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:25:32,055 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job build (981) in stage build is created (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:25:32,056 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job build status created recorded (no AI processing needed)
2025-05-30 17:25:32,056 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job build status created recorded'}
2025-05-30 17:25:35,390 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:35,390 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:35,391 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:35,392 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '09e5c713-2e25-4fa4-9502-271e956b2cfe', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'dcb0faa4-563c-41ab-8ac5-9b66ea70181a', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bcbb69e7-a1bb-4cd0-a5fe-9b4485c5d75e', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 17:25:35,392 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:35,392 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:35,394 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:35,394 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:35,394 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '09e5c713-2e25-4fa4-9502-271e956b2cfe', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'dcb0faa4-563c-41ab-8ac5-9b66ea70181a', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bcbb69e7-a1bb-4cd0-a5fe-9b4485c5d75e', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 17:25:35,395 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 979, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.352931886, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:25:35,397 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:25:35,397 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:25:35,398 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (979) in stage test is pending (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:25:35,398 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status pending recorded (no AI processing needed)
2025-05-30 17:25:35,399 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status pending recorded'}
2025-05-30 17:25:37,743 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:37,745 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:37,745 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:37,746 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '02559a4d-56df-491e-9ac2-b8e36697b126', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '2cd8fe0e-1893-4f66-9c69-548366a47bba', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bdd65900-bff8-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 17:25:37,746 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:37,746 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:37,747 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:37,748 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:37,748 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '02559a4d-56df-491e-9ac2-b8e36697b126', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '2cd8fe0e-1893-4f66-9c69-548366a47bba', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'bdd65900-bff8-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 17:25:37,749 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 980, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.504727494, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:25:37,751 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:25:37,751 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:25:37,752 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (980) in stage test is pending (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:25:37,752 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status pending recorded (no AI processing needed)
2025-05-30 17:25:37,753 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status pending recorded'}
2025-05-30 17:25:37,959 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:37,959 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:37,960 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:37,960 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '86de4f88-0896-4489-85db-dc6ee6d442e8', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'd14d57ba-1ddd-4b87-99b8-b6990e7bf32d', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b0c419a5-73ca-413f-b124-af7a6a148f55', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2106'}
2025-05-30 17:25:37,961 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:37,961 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:37,961 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:37,962 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:37,962 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '86de4f88-0896-4489-85db-dc6ee6d442e8', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'd14d57ba-1ddd-4b87-99b8-b6990e7bf32d', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b0c419a5-73ca-413f-b124-af7a6a148f55', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2106'}
2025-05-30 17:25:37,963 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 979, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'running', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': '2025-05-30 09:25:34 UTC', 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': '2025-05-30T09:25:34Z', 'build_finished_at_iso': None, 'build_duration': 0.434284601, 'build_queued_duration': 1.542042046, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:25:37,965 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:25:37,965 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:25:37,966 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (979) in stage test is running (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:25:37,966 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status running recorded (no AI processing needed)
2025-05-30 17:25:37,967 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status running recorded'}
2025-05-30 17:25:38,674 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:38,674 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:38,674 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:38,675 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '4a7b37d8-e92c-41bd-9f46-c6fc18180d0a', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '8f970516-e668-4d80-a1bc-e28262f81c5e', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '97cdce16-461d-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3245'}
2025-05-30 17:25:38,676 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:38,677 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:38,677 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:38,678 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:38,678 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '4a7b37d8-e92c-41bd-9f46-c6fc18180d0a', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '8f970516-e668-4d80-a1bc-e28262f81c5e', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '97cdce16-461d-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3245'}
2025-05-30 17:25:38,679 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 263, 'iid': 85, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'source': 'push', 'status': 'pending', 'detailed_status': 'pending', 'stages': ['test', 'build'], 'created_at': '2025-05-30 09:25:30 UTC', 'finished_at': None, 'duration': None, 'queued_duration': None, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/263'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 978)', 'timestamp': '2025-05-30T17:25:19+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 980, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-30 09:25:30 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 5.048833602, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 981, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-30 09:25:30 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 979, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-30 09:25:30 UTC', 'started_at': '2025-05-30 09:25:34 UTC', 'finished_at': None, 'duration': 4.125906133, 'queued_duration': 1.542042, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-30 17:25:38,681 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-30 17:25:38,681 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-30 17:25:38,682 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 263 for aider-plus-dev is pending (Project: ai-proxy, User: Longer)
2025-05-30 17:25:38,682 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 263 status pending recorded (no AI monitoring needed)
2025-05-30 17:25:38,683 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 263 status pending recorded'}
2025-05-30 17:25:39,608 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:25:39,609 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:25:39,609 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:25:39,610 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'f5d4d1e7-cf63-4ea3-9962-4fbe20b8d40d', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '880b2a71-abed-4712-86a4-c967207e26e9', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'da870177-6c7b-4ca1-8d8a-9b56cca8e9bc', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3242'}
2025-05-30 17:25:39,610 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:25:39,610 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:25:39,611 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:25:39,611 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:25:39,612 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'f5d4d1e7-cf63-4ea3-9962-4fbe20b8d40d', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '880b2a71-abed-4712-86a4-c967207e26e9', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'da870177-6c7b-4ca1-8d8a-9b56cca8e9bc', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3242'}
2025-05-30 17:25:39,613 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 263, 'iid': 85, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'source': 'push', 'status': 'running', 'detailed_status': 'running', 'stages': ['test', 'build'], 'created_at': '2025-05-30 09:25:30 UTC', 'finished_at': None, 'duration': None, 'queued_duration': 9, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/263'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 978)', 'timestamp': '2025-05-30T17:25:19+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 980, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-30 09:25:30 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 5.951422174, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 981, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-30 09:25:30 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 979, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-30 09:25:30 UTC', 'started_at': '2025-05-30 09:25:34 UTC', 'finished_at': None, 'duration': 5.028534235, 'queued_duration': 1.542042, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-30 17:25:39,615 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-30 17:25:39,615 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-30 17:25:39,616 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 263 for aider-plus-dev is running (Project: ai-proxy, User: Longer)
2025-05-30 17:25:39,616 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 263 status running recorded (no AI monitoring needed)
2025-05-30 17:25:39,617 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 263 status running recorded'}
2025-05-30 17:28:13,662 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:28:13,663 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:28:13,663 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:28:13,664 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'f0f1cd93-ca54-4f46-ac23-73c0237130b9', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '91198eca-17b1-4372-91de-515b00481ddd', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '5d9a9b2c-0d80-4087-8c1e-9ff79c7974db', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2147'}
2025-05-30 17:28:13,664 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:28:13,665 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:28:13,665 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:28:13,665 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:28:13,666 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'f0f1cd93-ca54-4f46-ac23-73c0237130b9', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '91198eca-17b1-4372-91de-515b00481ddd', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '5d9a9b2c-0d80-4087-8c1e-9ff79c7974db', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2147'}
2025-05-30 17:28:13,666 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 980, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'running', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': '2025-05-30 09:28:11 UTC', 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': '2025-05-30T09:28:11Z', 'build_finished_at_iso': None, 'build_duration': 0.725156638, 'build_queued_duration': 157.580672008, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 263, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'running', 'duration': None, 'started_at': '2025-05-30 09:25:39 UTC', 'finished_at': None, 'started_at_iso': '2025-05-30T09:25:39Z', 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:28:13,668 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:28:13,669 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:28:13,669 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (980) in stage test is running (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:28:13,670 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status running recorded (no AI processing needed)
2025-05-30 17:28:13,670 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status running recorded'}
2025-05-30 17:28:13,823 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 17:28:13,824 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 17:28:13,824 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 17:28:13,824 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '68fa98aa-0823-4f2f-85f8-e5f59e5ee637', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'f46328fd-af8b-4cae-a6ef-0745513f6e56', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'fafa782b-8434-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2178'}
2025-05-30 17:28:13,825 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 17:28:13,826 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 17:28:13,826 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 17:28:13,826 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 17:28:13,827 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '68fa98aa-0823-4f2f-85f8-e5f59e5ee637', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'f46328fd-af8b-4cae-a6ef-0745513f6e56', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'fafa782b-8434-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2178'}
2025-05-30 17:28:13,828 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': '276da7d1d761c8fb48ad935f77b22d9b2105fdaf', 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'retries_count': 0, 'build_id': 979, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'failed', 'build_created_at': '2025-05-30 09:25:30 UTC', 'build_started_at': '2025-05-30 09:25:34 UTC', 'build_finished_at': '2025-05-30 09:28:10 UTC', 'build_created_at_iso': '2025-05-30T09:25:30Z', 'build_started_at_iso': '2025-05-30T09:25:34Z', 'build_finished_at_iso': '2025-05-30T09:28:10Z', 'build_duration': 155.440158, 'build_queued_duration': 1.542042, 'build_allow_failure': False, 'build_failure_reason': 'script_failure', 'pipeline_id': 263, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 263, 'name': None, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'running', 'duration': None, 'started_at': '2025-05-30 09:25:39 UTC', 'finished_at': None, 'started_at_iso': '2025-05-30T09:25:39Z', 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 17:28:13,829 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 17:28:13,830 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 17:28:13,830 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (979) in stage test is failed (Pipeline: 263, Project: ai-proxy, User: Longer)
2025-05-30 17:28:13,831 - bot_agent.config.model_config - INFO - model_config.py:141 - log_current_config - 当前AI模型配置:
2025-05-30 17:28:13,831 - bot_agent.config.model_config - INFO - model_config.py:142 - log_current_config -   - 推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-30 17:28:13,833 - bot_agent.config.model_config - INFO - model_config.py:143 - log_current_config -   - 代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 17:28:13,833 - bot_agent.config.model_config - INFO - model_config.py:144 - log_current_config -   - 通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-30 17:28:13,834 - bot_agent.config.model_config - INFO - model_config.py:145 - log_current_config -   - API密钥已设置: 是
2025-05-30 17:28:13,835 - bot_agent.config.model_config - INFO - model_config.py:135 - validate_config - 模型配置验证结果: {'api_key': True, 'analysis_model': True, 'code_generation_model': True}
2025-05-30 17:28:13,836 - bot_agent.config.model_config - INFO - model_config.py:150 - log_current_config - ✅ 所有模型配置正常
2025-05-30 17:28:13,836 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:48 - __init__ - AI Task analyzer initialized with analysis model: deepseek/deepseek-r1:free
2025-05-30 17:28:13,837 - bot_agent.dispatcher.router - INFO - router.py:24 - __init__ - Task router initialized
2025-05-30 17:28:28,456 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:181 - _ai_analyze_task - AI分析响应: ```json
{
  "task_type": "bug_fix",
  "confidence": 0.95,
  "priority": "high",
  "complexity": "medium",
  "reasoning": "该任务需要分析CI/CD作业失败原因并提供修复方案，符合'bug_fix'类型特征。关键词'失败分析''错误信息''修复建议'表明需要诊断和解决问题。优先级高因为失败的测试作业会阻塞开发流程，复杂度中等需要结合日志分析和代码调试",
  "risks": [
    {
      "type": "诊断不准确",
      "level": "medium",
      "description": "可能存在日志信息不全或偶发问题导致根本原因判断错误"
    },
    {
      "type": "修复方案失效",
      "level": "high",
      "description": "错误的修复可能导致测试流程持续失败或引入新问题"
    }
  ]
}
```
2025-05-30 17:28:28,458 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:197 - _ai_analyze_task - AI任务分析成功: {'task_type': 'bug_fix', 'confidence': 0.95, 'priority': 'high', 'complexity': 'medium', 'reasoning': "该任务需要分析CI/CD作业失败原因并提供修复方案，符合'bug_fix'类型特征。关键词'失败分析''错误信息''修复建议'表明需要诊断和解决问题。优先级高因为失败的测试作业会阻塞开发流程，复杂度中等需要结合日志分析和代码调试", 'risks': [{'type': '诊断不准确', 'level': 'medium', 'description': '可能存在日志信息不全或偶发问题导致根本原因判断错误'}, {'type': '修复方案失效', 'level': 'high', 'description': '错误的修复可能导致测试流程持续失败或引入新问题'}]}
2025-05-30 17:28:28,459 - bot_agent.dispatcher.router - INFO - router.py:74 - route_task - Task 02f28cda-45dd-41a6-b8b4-07db81d5c57c routed to aider: 作业失败分析 - test (Job 979) (type: TaskType.BUG_FIX, priority: TaskPriority.HIGH)
2025-05-30 17:28:28,459 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:28:28,460 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:28:28,637 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:28:28,638 - bot_agent.handlers.ai_response_handler - INFO - ai_response_handler.py:22 - __init__ - AI response handler initialized
2025-05-30 17:28:28,638 - bot_agent.handlers.information_query_handler - INFO - information_query_handler.py:28 - __init__ - InformationQueryHandler initialized
2025-05-30 17:28:28,638 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:28:28,639 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:28:28,792 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:28:28,793 - bot_agent.deployment.pipeline_analyzer - INFO - pipeline_analyzer.py:48 - __init__ - PipelineAnalyzer initialized
2025-05-30 17:28:28,794 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:28:28,794 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:28:29,002 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:28:29,002 - bot_agent.handlers.ai_response_handler - INFO - ai_response_handler.py:22 - __init__ - AI response handler initialized
2025-05-30 17:28:29,003 - bot_agent.deployment.deployment_task_executor - INFO - deployment_task_executor.py:34 - __init__ - DeploymentTaskExecutor initialized
2025-05-30 17:28:29,003 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:28:29,003 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:28:29,302 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:28:29,304 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:47 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-30 17:28:29,305 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-30 17:28:29,305 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-30 17:28:29,306 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-30 17:28:29,306 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-30 17:28:29,307 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-30 17:28:29,307 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-30 17:28:29,308 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-30 17:28:29,308 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:63 - __init__ - AI processor initialized with Git repository directory: E:\aider-git-repos\
2025-05-30 17:28:29,308 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:82 - process_task - Processing task 02f28cda-45dd-41a6-b8b4-07db81d5c57c with aider
2025-05-30 17:28:29,309 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:136 - _process_with_aider - 使用AiderBasedTaskExecutor处理任务: 作业失败分析 - test (Job 979)
2025-05-30 17:28:29,309 - bot_agent.engines.simple_progress_tracker - WARNING - simple_progress_tracker.py:50 - send_progress_update - 缺少项目ID或Issue IID，跳过进度更新
2025-05-30 17:28:29,310 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 17:28:29,310 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 17:28:29,465 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 17:28:29,466 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-30 17:28:29,466 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-30 17:28:29,467 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-30 17:28:29,467 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-30 17:28:29,468 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-30 17:28:29,469 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-30 17:28:29,469 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-30 17:28:29,470 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: information_query
2025-05-30 17:28:29,470 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TerminalTools
2025-05-30 17:28:29,470 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TestingTools
2025-05-30 17:28:29,471 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: LogAnalysisTools
2025-05-30 17:28:29,472 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DatabaseTools
2025-05-30 17:28:29,473 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DependencyTools
2025-05-30 17:28:29,474 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DebugTools
2025-05-30 17:28:29,474 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: FrontendDebugTools
2025-05-30 17:28:29,475 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: RefactorTools
2025-05-30 17:28:29,475 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DocumentationTools
2025-05-30 17:28:29,476 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: SecurityTools
2025-05-30 17:28:29,476 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: CodeGenerationTools
2025-05-30 17:28:29,477 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-30 17:28:29,477 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-30 17:28:29,478 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-30 17:28:29,479 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-30 17:28:29,479 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-30 17:28:29,480 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-30 17:28:29,480 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:174 - _process_with_aider - 使用Aider执行引擎处理 TaskType.BUG_FIX 类型任务
2025-05-30 17:28:29,481 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 02f28cda-45dd-41a6-b8b4-07db81d5c57c
2025-05-30 17:28:29,484 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-30 17:28:29,485 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-30 17:28:29,486 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-30 17:28:29,486 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-30 17:28:30,651 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 17:28:30,652 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-30T09:25:27.918Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-30T09:25:27.918Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-30 17:28:30,655 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:30,656 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-30 17:28:30,656 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:30,657 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-30 17:28:30,657 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 1.18s
2025-05-30 17:28:30,661 - bot_agent.memory.project_memory - INFO - project_memory.py:42 - __init__ - ProjectMemoryManager initialized for project: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:30,676 - bot_agent.memory.global_memory - INFO - global_memory.py:60 - load_memory - Global memory loaded
2025-05-30 17:28:30,677 - bot_agent.memory.memory_integration - INFO - memory_integration.py:78 - prepare_context_for_task - Prepared context for task: 926 characters
2025-05-30 17:28:30,677 - bot_agent.engines.tool_integration - INFO - tool_integration.py:49 - enhance_aider_request - 开始增强Aider请求
2025-05-30 17:28:30,678 - bot_agent.tools.tool_router - INFO - tool_router.py:184 - analyze_task - 分析任务: 
任务类型: TaskType.BUG_FIX
任务标题: 作业失败分析 - test (Job 979)

任务描述:

## 🚨 作业失败自动分析

**项目**: ai-proxy
**作业名称**: test
**作业ID**: 979
**Pipeline ID**: 263
**阶段**: test
**分支**: aider-plus-dev
**状态**: failed
**失败原因**: script_failure

### 任务要求
1. 分析作业失败的具体原因
2. 收集作业日志和错误信息
3. 提供针对性的修复建议
4. 如果是部署作业，评估回滚需求

### 自动化指令
请立即分析Job 979的失败原因，收集详细日志，并提供修复方案。


# 全局工作记忆
## 相关工作习惯
- task_preference: 成功处理 TaskType.BUG_FIX 类型任务 (频率: 110)
- task_preference: 成功处理 TaskType.PROJECT_ANALYSIS 类型任务 (频率: 4)
- task_preference: 成功处理 OPTIMIZATION 类型任务 (频率: 1)

## 偏好设置
tools: VSCode, PowerShell, GitLab, 2025-05-27T15:23:37.010003, GitLab, aider-plus; programming_languages: Python, JavaScript, Rust, 2025-05-30T17:25:25.233422, go, bug_fix, 1; frameworks: FastAPI, PostgreSQL, Redis, pytest, 2025-05-30T10:14:58.547585, fastapi, 作业失败分析 - test (Job 959), 1


# 项目记忆
## 项目架构
Overview: AI API代理服务; Patterns: 代理模式, 策略模式, 工厂模式; Technologies: FastAPI, asyncio, httpx

## general 编码规范
- 作业失败分析 - lint (Job 653)
- 作业失败分析 - lint (Job 664)
- 作业失败分析 - lint (Job 677)



# 环境信息
## 环境配置
- last_project_path: E:\aider-git-repos\ai-proxy
- last_task_type: bug_fix
- os: Windows 11
- python_version: 3.9
- conda_env: aider-plus
- git_user: aider-worker
- workspace: E:\Projects\aider-plus
- last_used_tool: aider


## 🚨 严格执行指令 - 禁止偏离

### ⚠️ 绝对禁止的行为：
1. **禁止创建任何新文件** - 不允许创建.py、.js、.md等任何新文件
2. **禁止编写代码** - 不允许编写类、函数、测试代码
3. **禁止基于假设分析** - 必须基于真实数据和日志
4. **禁止提供通用建议** - 必须针对具体问题提供具体解决方案
5. **禁止代码审查** - 当前任务不是代码审查，是问题分析

### ✅ 必须执行的步骤（严格按顺序）：

#### 第1步：获取真实数据（必须完成）
- 使用GitLabClient获取Job的实际日志内容
- 如果无法获取，明确说明原因并停止
- 不允许基于"没有日志"进行假设性分析

#### 第2步：分析具体问题（基于真实数据）
- 使用LogAnalysisTools分析实际的错误日志
- 识别具体的错误类型、文件、行号
- 确定失败的根本原因

#### 第3步：执行具体修复（针对性解决）
- 使用TerminalTools执行针对性的修复命令
- 修复具体识别出的问题
- 不执行通用的格式化命令

#### 第4步：验证修复效果
- 使用TestingTools验证修复是否成功
- 确认问题已解决

### 🎯 当前任务要求：
如果这是作业失败分析任务，你必须：
1. 获取指定Job ID的实际失败日志
2. 分析日志中的具体错误信息
3. 提供针对这些具体错误的修复方案
4. 验证修复效果

### 🚫 严格禁止：
- 说"Since we don't have the actual log files"
- 提供black、flake8等通用命令
- 创建LintAnalyzer等新类
- 进行代码审查
- 创建测试文件

现在开始执行，严格遵循上述要求：

2025-05-30 17:28:30,685 - bot_agent.tools.tool_router - INFO - tool_router.py:217 - analyze_task - 生成了 6 个工具建议
2025-05-30 17:28:30,685 - bot_agent.engines.tool_integration - INFO - tool_integration.py:67 - enhance_aider_request - 请求增强完成，添加了 6 个工具建议
2025-05-30 17:28:30,686 - bot_agent.engines.task_executor - INFO - task_executor.py:252 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 17:28:30,709 - bot_agent.engines.task_executor - INFO - task_executor.py:404 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-30 17:28:30,831 - bot_agent.engines.task_executor - INFO - task_executor.py:425 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_provider_security.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_lint_analysis.py', 'requirements.txt', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_error.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_health_check.py', 'tests\\test_proxy_service_error.py', 'api_proxy\\config.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_proxy_service_integration.py', 'setup.py', 'tests\\test_job_analysis_integration.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_provider_initialization.py', 'api_proxy\\__init__.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_failure_analysis_integration.py', 'api_proxy\\models.py']
2025-05-30 17:28:30,832 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 🔍 启用Aider操作监控系统
2025-05-30 17:28:32,661 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:49 - log_operation - [AIDER_MONITOR] ✅ coder_created: 创建监控的Coder
2025-05-30 17:28:32,661 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:53 - log_operation - [AIDER_MONITOR]   kwargs: {'main_model': Model(name='openrouter/deepseek/deepseek-chat-v3-0324:free', edit_format='diff', weak_model_name='openrouter/deepseek/deepseek-chat-v3-0324:free', use_repo_map=True, send_undo_reply=False, lazy=False, overeager=False, reminder='user', examples_as_sys_msg=True, extra_params=None, cache_control=False, caches_by_default=True, use_system_prompt=True, use_temperature=0.3, streaming=True, editor_model_name='openrouter/deepseek/deepseek-r1:free', editor_edit_format='editor-diff', reasoning_tag=None, remove_reasoning=None, system_prompt_prefix=None, accepts_settings=[]), 'io': <bot_agent.aider_extensions.aider_monitor.MonitoredIO object at 0x000002762AE03C80>, 'repo': <aider.repo.GitRepo object at 0x00000276209237A0>, 'fnames': ['tests\\test_provider_security.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_lint_analysis.py', 'requirements.txt', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_error.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_health_check.py', 'tests\\test_proxy_service_error.py', 'api_proxy\\config.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_proxy_service_integration.py', 'setup.py', 'tests\\test_job_analysis_integration.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_provider_initialization.py', 'api_proxy\\__init__.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_failure_analysis_integration.py', 'api_proxy\\models.py'], 'use_git': False, 'auto_commits': False, 'dirty_commits': False, 'auto_lint': False, 'auto_test': False, 'stream': False, 'verbose': False, 'chat_language': 'Chinese', 'suggest_shell_commands': False, 'auto_accept_architect': True, 'map_tokens': 0, 'cache_prompts': False, 'num_cache_warming_pings': 0}
2025-05-30 17:28:32,663 - bot_agent.engines.task_executor - INFO - task_executor.py:302 - _execute_with_aider - 使用优化的Aider执行任务: 作业失败分析 - test (Job 979)
2025-05-30 17:28:32,666 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-30 17:28:32,666 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-30 17:28:32,667 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-30 17:28:32,668 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-30 17:28:33,150 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 17:28:33,151 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-30T09:25:27.918Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-30T09:25:27.918Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-30 17:28:33,154 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:33,155 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-30 17:28:33,156 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:33,156 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-30 17:28:33,157 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 0.49s
2025-05-30 17:28:33,157 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: task_1748597313_1748597313
2025-05-30 17:28:33,158 - bot_agent.engines.task_executor - INFO - task_executor.py:773 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-30 17:28:33,159 - bot_agent.engines.task_executor - INFO - task_executor.py:777 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-30 17:28:33,163 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: job_failure_analysis
2025-05-30 17:28:33,164 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始作业失败分析
2025-05-30 17:28:33,165 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 提取Job ID
2025-05-30 17:28:33,165 - bot_agent.engines.task_executor - INFO - task_executor.py:1063 - _handle_job_failure_analysis - 提取到Job ID: 979
2025-05-30 17:28:33,166 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 成功提取Job ID: 979
2025-05-30 17:28:33,167 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取项目ID
2025-05-30 17:28:33,167 - bot_agent.engines.task_executor - INFO - task_executor.py:1071 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-30 17:28:33,168 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取到项目ID: 9
2025-05-30 17:28:33,168 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 准备工作空间
2025-05-30 17:28:33,169 - bot_agent.engines.task_executor - INFO - task_executor.py:1086 - _handle_job_failure_analysis - 作业失败分析使用项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:33,170 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:33,171 - bot_agent.engines.task_executor - INFO - task_executor.py:1092 - _handle_job_failure_analysis - 已更新会话 task_1748597313_1748597313 的项目路径为: E:\aider-git-repos\ai-proxy
2025-05-30 17:28:33,171 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 会话路径已更新
2025-05-30 17:28:33,172 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始智能分析
2025-05-30 17:28:33,173 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 预执行数据收集
2025-05-30 17:28:33,174 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:70 - get_job_info_and_log - 🔍 获取Job 979的信息和日志...
2025-05-30 17:28:33,174 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:437 - get_job - Getting job 979 in project 9
2025-05-30 17:28:33,175 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9/jobs/979
2025-05-30 17:28:34,543 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 17:28:34,544 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 979, 'status': 'failed', 'stage': 'test', 'name': 'test', 'ref': 'aider-plus-dev', 'tag': False, 'coverage': None, 'allow_failure': False, 'created_at': '2025-05-30T09:25:30.116Z', 'started_at': '2025-05-30T09:25:34.922Z', 'finished_at': '2025-05-30T09:28:10.362Z', 'erased_at': None, 'duration': 155.440158, 'queued_duration': 1.542042, 'user': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer', 'created_at': '2025-04-27T02:44:28.626Z', 'bio': '', 'location': '', 'public_email': None, 'skype': '', 'linkedin': '', 'twitter': '', 'discord': '', 'website_url': '', 'organization': '', 'job_title': '', 'pronouns': None, 'bot': False, 'work_information': None, 'followers': 0, 'following': 0, 'local_time': None}, 'commit': {'id': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'short_id': '55d81d5e', 'created_at': '2025-05-30T17:25:19.000+08:00', 'parent_ids': ['276da7d1d761c8fb48ad935f77b22d9b2105fdaf'], 'title': 'AI自动修改: 作业失败分析 - lint (Job 978)', 'message': 'AI自动修改: 作业失败分析 - lint (Job 978)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'authored_date': '2025-05-30T17:25:19.000+08:00', 'committer_name': 'Longer (aider)', 'committer_email': '<EMAIL>', 'committed_date': '2025-05-30T17:25:19.000+08:00', 'trailers': {}, 'extended_trailers': {}, 'web_url': 'http://***************/Longer/ai-proxy/-/commit/55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864'}, 'pipeline': {'id': 263, 'iid': 85, 'project_id': 9, 'sha': '55d81d5e7df2bb6d4004d01256fe2e1c4bcc0864', 'ref': 'aider-plus-dev', 'status': 'running', 'source': 'push', 'created_at': '2025-05-30T09:25:30.047Z', 'updated_at': '2025-05-30T09:25:39.179Z', 'web_url': 'http://***************/Longer/ai-proxy/-/pipelines/263'}, 'failure_reason': 'script_failure', 'web_url': 'http://***************/Longer/ai-proxy/-/jobs/979', 'project': {'ci_job_token_scope_enabled': False}, 'artifacts': [], 'runner': {'id': 1, 'description': 'docker-runner-137', 'ip_address': None, 'active': True, 'paused': False, 'is_shared': True, 'runner_type': 'instance_type', 'name': None, 'online': True, 'status': 'online'}, 'runner_manager': {'id': 2, 'system_id': 'r_zhwE8WyyfNZL', 'version': '17.11.0', 'revision': '0f67ff19', 'platform': 'linux', 'architecture': 'amd64', 'created_at': '2025-04-27T09:00:17.697Z', 'contacted_at': '2025-05-30T09:28:16.515Z', 'ip_address': '***************', 'status': 'online'}, 'artifacts_expire_at': None, 'archived': False, 'tag_list': []}
2025-05-30 17:28:34,546 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:444 - get_job - 成功获取作业信息: Job 979 - test (failed)
2025-05-30 17:28:34,547 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:466 - get_job_log - Getting log for job 979 in project 9
2025-05-30 17:28:34,935 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:484 - get_job_log - 成功获取作业日志: Job 979, 长度: 14256 字符
2025-05-30 17:28:34,936 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-30 17:28:34,937 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:535 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-30 17:28:34,938 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:262 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwk02lyrz.log']
2025-05-30 17:28:34,967 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:550 - analyze_job_log - ✅ 作业日志分析完成
2025-05-30 17:28:34,970 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 调用AI进行智能分析
2025-05-30 17:28:34,971 - bot_agent.engines.task_executor - INFO - task_executor.py:1198 - _handle_job_failure_analysis - 🧠 开始使用Bot推理分析器进行作业失败分析...
2025-05-30 17:28:34,972 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:60 - analyze_job_failure - 🧠 开始Bot级别推理分析...
2025-05-30 17:28:36,017 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:155 - _call_reasoning_model - 🧠 调用推理模型: deepseek/deepseek-r1:free
2025-05-30 17:28:44,379 - bot_agent.analyzers.bot_reasoning_analyzer - ERROR - bot_reasoning_analyzer.py:180 - _call_reasoning_model - 调用推理模型失败: Error code: 429 - {'error': {'message': 'Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day', 'code': 429, 'metadata': {'headers': {'X-RateLimit-Limit': '50', 'X-RateLimit-Remaining': '0', 'X-RateLimit-Reset': '1748649600000'}, 'provider_name': None}}, 'user_id': 'user_2vZFoBRieU14Pgp19lBoJwpuhFQ'}
2025-05-30 17:28:44,379 - bot_agent.analyzers.bot_reasoning_analyzer - ERROR - bot_reasoning_analyzer.py:83 - analyze_job_failure - 推理分析失败: Error code: 429 - {'error': {'message': 'Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day', 'code': 429, 'metadata': {'headers': {'X-RateLimit-Limit': '50', 'X-RateLimit-Remaining': '0', 'X-RateLimit-Reset': '1748649600000'}, 'provider_name': None}}, 'user_id': 'user_2vZFoBRieU14Pgp19lBoJwpuhFQ'}
2025-05-30 17:28:44,380 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - Bot推理分析失败，使用备用方案
2025-05-30 17:28:44,385 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 启动智能修复协调器
2025-05-30 17:28:44,386 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 执行智能修复路由分析
2025-05-30 17:28:44,386 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:47 - execute_intelligent_fix - 🚀 启动智能修复协调器...
2025-05-30 17:28:44,576 - bot_agent.tools.intelligent_fix_coordinator - WARNING - intelligent_fix_coordinator.py:342 - _collect_git_info - 收集Git信息失败: 'NoneType' object has no attribute 'strip'
2025-05-30 17:28:44,577 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:223 - cached_operation - 💾 缓存存储: gather_project_context
2025-05-30 17:28:44,579 - bot_agent.tools.performance_monitor - INFO - performance_monitor.py:258 - controlled_ai_call - 🤖 开始AI调用: intelligent_fix_routing
2025-05-30 17:28:44,579 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:99 - analyze_and_route - 🧠 开始智能修复路由分析...
2025-05-30 17:28:44,582 - bot_agent.tools.error_classifier - INFO - error_classifier.py:91 - classify_errors - ✅ 成功分类 24 个错误
2025-05-30 17:28:44,582 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:110 - analyze_and_route - 📊 错误分类完成: 24 个错误
2025-05-30 17:28:44,586 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:264 - _deep_reasoning_analysis - 🚀 启动增强LLM调用...
2025-05-30 17:28:44,587 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:228 - call_reasoning_ai - 🔥 开始调用DeepSeek R1推理模型进行智能路由分析...
2025-05-30 17:28:45,681 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:240 - call_reasoning_ai - 📤 发送请求到OpenRouter API: deepseek/deepseek-r1:free
2025-05-30 17:28:48,825 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:251 - call_reasoning_ai - 📥 收到API响应: status=429
2025-05-30 17:28:48,825 - bot_agent.tools.intelligent_fix_router - ERROR - intelligent_fix_router.py:260 - call_reasoning_ai - ❌ API调用失败: 429, 响应: {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1748649600000"},"provider_name":null}},"user_id":"user_2vZFoBRieU14Pgp19lBoJwpuhFQ"}
2025-05-30 17:28:48,829 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 1): API调用失败: 429, 响应: {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1748649600000"},"provider_name":null}},"user_id":"user_2vZFoBRieU14Pgp19lBoJwpuhFQ"}
2025-05-30 17:28:48,829 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 3.4 秒后重试...
2025-05-30 17:28:52,270 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:228 - call_reasoning_ai - 🔥 开始调用DeepSeek R1推理模型进行智能路由分析...
2025-05-30 17:28:53,040 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:240 - call_reasoning_ai - 📤 发送请求到OpenRouter API: deepseek/deepseek-r1:free
2025-05-30 17:28:55,885 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:251 - call_reasoning_ai - 📥 收到API响应: status=429
2025-05-30 17:28:55,886 - bot_agent.tools.intelligent_fix_router - ERROR - intelligent_fix_router.py:260 - call_reasoning_ai - ❌ API调用失败: 429, 响应: {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1748649600000"},"provider_name":null}},"user_id":"user_2vZFoBRieU14Pgp19lBoJwpuhFQ"}
2025-05-30 17:28:55,890 - bot_agent.utils.llm_retry_handler - WARNING - llm_retry_handler.py:150 - execute_with_retry - LLM调用失败 (尝试 2): API调用失败: 429, 响应: {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1748649600000"},"provider_name":null}},"user_id":"user_2vZFoBRieU14Pgp19lBoJwpuhFQ"}
2025-05-30 17:28:55,890 - bot_agent.utils.llm_retry_handler - INFO - llm_retry_handler.py:151 - execute_with_retry - 将在 7.4 秒后重试...
2025-05-30 17:29:03,341 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:228 - call_reasoning_ai - 🔥 开始调用DeepSeek R1推理模型进行智能路由分析...
2025-05-30 17:29:04,050 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:240 - call_reasoning_ai - 📤 发送请求到OpenRouter API: deepseek/deepseek-r1:free
2025-05-30 17:29:06,326 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📊 当前监控状态:
2025-05-30 17:29:06,327 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log -    job_failure_analysis:
2025-05-30 17:29:06,327 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log -      执行时间: 33.17s
2025-05-30 17:29:06,328 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log -      无更新时间: 21.94s
2025-05-30 17:29:06,328 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log -      调用次数: 1
