2025-05-30 10:07:57,049 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-30 10:07:57,050 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-30 10:07:57,050 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-30 10:07:57,051 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:73 - _setup_signal_handlers - 信号处理器设置完成
2025-05-30 10:07:57,051 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:61 - __init__ - DeadlockMonitor初始化完成，检查间隔: 2.0s，最大执行时间: 30.0s
2025-05-30 10:07:57,053 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 10:07:57,054 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 10:07:57,884 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 10:07:57,885 - bot_agent.engines.simple_progress_tracker - INFO - simple_progress_tracker.py:31 - __init__ - SimpleProgressTracker initialized
2025-05-30 10:07:57,885 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:136 - _process_with_aider - 使用AiderBasedTaskExecutor处理任务: 作业失败分析 - lint (Job 958)
2025-05-30 10:07:57,885 - bot_agent.engines.simple_progress_tracker - WARNING - simple_progress_tracker.py:50 - send_progress_update - 缺少项目ID或Issue IID，跳过进度更新
2025-05-30 10:07:57,886 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:101 - start_monitoring - 死循环监控已启动
2025-05-30 10:07:57,887 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 10:07:57,887 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 10:07:58,110 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 10:07:58,111 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:99 - __init__ - 使用环境变量中的项目下载目录: E:\aider-git-repos\
2025-05-30 10:07:58,112 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:112 - __init__ - Git仓库工作目录: E:\aider-git-repos\
2025-05-30 10:07:58,112 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:121 - setup_git_config - 检查Git用户配置
2025-05-30 10:07:58,112 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:132 - setup_git_config - 使用环境变量中的Git用户名: aider-worker
2025-05-30 10:07:58,112 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:136 - setup_git_config - 使用环境变量中的Git用户邮箱: <EMAIL>
2025-05-30 10:07:58,113 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:143 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_NAME=aider-worker, GIT_COMMITTER_NAME=aider-worker
2025-05-30 10:07:58,114 - bot_agent.utils.gitlab_branch_manager - INFO - gitlab_branch_manager.py:148 - setup_git_config - 已设置Git环境变量: GIT_AUTHOR_EMAIL=<EMAIL>, GIT_COMMITTER_EMAIL=<EMAIL>
2025-05-30 10:07:58,115 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: information_query
2025-05-30 10:07:58,115 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TerminalTools
2025-05-30 10:07:58,115 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: TestingTools
2025-05-30 10:07:58,116 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: LogAnalysisTools
2025-05-30 10:07:58,116 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DatabaseTools
2025-05-30 10:07:58,116 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DependencyTools
2025-05-30 10:07:58,117 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DebugTools
2025-05-30 10:07:58,117 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: FrontendDebugTools
2025-05-30 10:07:58,117 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: RefactorTools
2025-05-30 10:07:58,118 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: DocumentationTools
2025-05-30 10:07:58,118 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: SecurityTools
2025-05-30 10:07:58,118 - bot_agent.tools.base_tool - INFO - base_tool.py:137 - register - 注册工具: CodeGenerationTools
2025-05-30 10:07:58,119 - bot_agent.tools.tool_router - INFO - tool_router.py:75 - _register_tools - 注册了 12 个工具
2025-05-30 10:07:58,119 - bot_agent.memory.global_memory - INFO - global_memory.py:44 - __init__ - GlobalMemoryManager initialized with directory: E:\Projects\aider-plus\bot_agent\memory\global
2025-05-30 10:07:58,122 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:22 - __init__ - MemoryAnalyzer initialized
2025-05-30 10:07:58,122 - bot_agent.memory.memory_integration - INFO - memory_integration.py:31 - __init__ - MemoryIntegration initialized
2025-05-30 10:07:58,122 - bot_agent.engines.task_executor - INFO - task_executor.py:50 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-30 10:07:58,123 - bot_agent.engines.task_executor - INFO - task_executor.py:51 - __init__ - 🔍 死循环监控已启动
2025-05-30 10:07:58,124 - bot_agent.handlers.ai_processor - INFO - ai_processor.py:174 - _process_with_aider - 使用Aider执行引擎处理 TaskType.BUG_FIX 类型任务
2025-05-30 10:07:58,124 - bot_agent.engines.task_executor - INFO - task_executor.py:64 - execute_task - 开始使用Aider执行任务 4cd55598-7c1f-49db-be9a-2ca60a75a11d
2025-05-30 10:07:58,310 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-30 10:07:58,311 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-30 10:07:58,312 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-30 10:07:58,312 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-30 10:08:00,258 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 10:08:00,259 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-29T12:49:40.103Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-29T13:23:13.427Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-30 10:08:00,261 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:00,261 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-30 10:08:00,263 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:00,263 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-30 10:08:00,264 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 2.14s
2025-05-30 10:08:01,575 - bot_agent.memory.project_memory - INFO - project_memory.py:42 - __init__ - ProjectMemoryManager initialized for project: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:01,597 - bot_agent.memory.global_memory - INFO - global_memory.py:60 - load_memory - Global memory loaded
2025-05-30 10:08:01,597 - bot_agent.memory.memory_integration - INFO - memory_integration.py:78 - prepare_context_for_task - Prepared context for task: 926 characters
2025-05-30 10:08:01,598 - bot_agent.engines.tool_integration - INFO - tool_integration.py:49 - enhance_aider_request - 开始增强Aider请求
2025-05-30 10:08:01,598 - bot_agent.tools.tool_router - INFO - tool_router.py:184 - analyze_task - 分析任务: 
任务类型: TaskType.BUG_FIX
任务标题: 作业失败分析 - lint (Job 958)

任务描述:

## 🚨 作业失败自动分析

**项目**: ai-proxy
**作业名称**: lint
**作业ID**: 958
**Pipeline ID**: 257
**阶段**: test
**分支**: aider-plus-dev
**状态**: failed
**失败原因**: script_failure

### 任务要求
1. 分析作业失败的具体原因
2. 收集作业日志和错误信息
3. 提供针对性的修复建议
4. 如果是部署作业，评估回滚需求

### 自动化指令
请立即分析Job 958的失败原因，收集详细日志，并提供修复方案。


# 全局工作记忆
## 相关工作习惯
- task_preference: 成功处理 TaskType.BUG_FIX 类型任务 (频率: 103)
- task_preference: 成功处理 TaskType.PROJECT_ANALYSIS 类型任务 (频率: 4)
- task_preference: 成功处理 OPTIMIZATION 类型任务 (频率: 1)

## 偏好设置
tools: VSCode, PowerShell, GitLab, 2025-05-27T15:23:37.010003, GitLab, aider-plus; programming_languages: Python, JavaScript, Rust, 2025-05-29T22:05:42.493613, go, bug_fix, 1; frameworks: FastAPI, PostgreSQL, Redis, pytest, 2025-05-29T19:05:08.557516, express, 作业失败分析 - lint (Job 932), 1


# 项目记忆
## 项目架构
Overview: AI API代理服务; Patterns: 代理模式, 策略模式, 工厂模式; Technologies: FastAPI, asyncio, httpx

## general 编码规范
- 作业失败分析 - lint (Job 653)
- 作业失败分析 - lint (Job 664)
- 作业失败分析 - lint (Job 677)



# 环境信息
## 环境配置
- last_project_path: E:\aider-git-repos\ai-proxy
- last_task_type: bug_fix
- os: Windows 11
- python_version: 3.9
- conda_env: aider-plus
- git_user: aider-worker
- workspace: E:\Projects\aider-plus
- last_used_tool: aider


## 🚨 严格执行指令 - 禁止偏离

### ⚠️ 绝对禁止的行为：
1. **禁止创建任何新文件** - 不允许创建.py、.js、.md等任何新文件
2. **禁止编写代码** - 不允许编写类、函数、测试代码
3. **禁止基于假设分析** - 必须基于真实数据和日志
4. **禁止提供通用建议** - 必须针对具体问题提供具体解决方案
5. **禁止代码审查** - 当前任务不是代码审查，是问题分析

### ✅ 必须执行的步骤（严格按顺序）：

#### 第1步：获取真实数据（必须完成）
- 使用GitLabClient获取Job的实际日志内容
- 如果无法获取，明确说明原因并停止
- 不允许基于"没有日志"进行假设性分析

#### 第2步：分析具体问题（基于真实数据）
- 使用LogAnalysisTools分析实际的错误日志
- 识别具体的错误类型、文件、行号
- 确定失败的根本原因

#### 第3步：执行具体修复（针对性解决）
- 使用TerminalTools执行针对性的修复命令
- 修复具体识别出的问题
- 不执行通用的格式化命令

#### 第4步：验证修复效果
- 使用TestingTools验证修复是否成功
- 确认问题已解决

### 🎯 当前任务要求：
如果这是作业失败分析任务，你必须：
1. 获取指定Job ID的实际失败日志
2. 分析日志中的具体错误信息
3. 提供针对这些具体错误的修复方案
4. 验证修复效果

### 🚫 严格禁止：
- 说"Since we don't have the actual log files"
- 提供black、flake8等通用命令
- 创建LintAnalyzer等新类
- 进行代码审查
- 创建测试文件

现在开始执行，严格遵循上述要求：

2025-05-30 10:08:01,604 - bot_agent.tools.tool_router - INFO - tool_router.py:217 - analyze_task - 生成了 6 个工具建议
2025-05-30 10:08:01,605 - bot_agent.engines.tool_integration - INFO - tool_integration.py:67 - enhance_aider_request - 请求增强完成，添加了 6 个工具建议
2025-05-30 10:08:01,605 - bot_agent.engines.task_executor - INFO - task_executor.py:252 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 10:08:07,913 - bot_agent.engines.task_executor - INFO - task_executor.py:404 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-30 10:08:08,027 - bot_agent.engines.task_executor - INFO - task_executor.py:425 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_analysis_error.py', 'tests\\test_provider_initialization.py', 'tests\\test_health_check.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'api_proxy\\models.py', 'tests\\test_provider_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_error.py', 'setup.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_job_lint_analysis.py', 'tests\\test_job_failure_analysis_boundary.py', 'api_proxy\\config.py', 'tests\\test_lint_analysis_unit.py', 'requirements.txt', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_boundary.py']
2025-05-30 10:08:08,031 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 🔍 启用Aider操作监控系统
2025-05-30 10:08:09,584 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:49 - log_operation - [AIDER_MONITOR] ✅ coder_created: 创建监控的Coder
2025-05-30 10:08:09,585 - bot_agent.aider_extensions.aider_monitor - INFO - aider_monitor.py:53 - log_operation - [AIDER_MONITOR]   kwargs: {'main_model': Model(name='openrouter/deepseek/deepseek-chat-v3-0324:free', edit_format='diff', weak_model_name='openrouter/deepseek/deepseek-chat-v3-0324:free', use_repo_map=True, send_undo_reply=False, lazy=False, overeager=False, reminder='user', examples_as_sys_msg=True, extra_params=None, cache_control=False, caches_by_default=True, use_system_prompt=True, use_temperature=0.3, streaming=True, editor_model_name='openrouter/deepseek/deepseek-r1:free', editor_edit_format='editor-diff', reasoning_tag=None, remove_reasoning=None, system_prompt_prefix=None, accepts_settings=[]), 'io': <bot_agent.aider_extensions.aider_monitor.MonitoredIO object at 0x0000022B5515F230>, 'repo': <aider.repo.GitRepo object at 0x0000022B5515DBE0>, 'fnames': ['tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_analysis_error.py', 'tests\\test_provider_initialization.py', 'tests\\test_health_check.py', 'tests\\test_sensitive_data.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'api_proxy\\models.py', 'tests\\test_provider_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_error.py', 'setup.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_job_lint_analysis.py', 'tests\\test_job_failure_analysis_boundary.py', 'api_proxy\\config.py', 'tests\\test_lint_analysis_unit.py', 'requirements.txt', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_boundary.py'], 'use_git': False, 'auto_commits': False, 'dirty_commits': False, 'auto_lint': False, 'auto_test': False, 'stream': False, 'verbose': False, 'chat_language': 'Chinese', 'suggest_shell_commands': False, 'auto_accept_architect': True, 'map_tokens': 0, 'cache_prompts': False, 'num_cache_warming_pings': 0}
2025-05-30 10:08:09,586 - bot_agent.engines.task_executor - INFO - task_executor.py:302 - _execute_with_aider - 使用优化的Aider执行任务: 作业失败分析 - lint (Job 958)
2025-05-30 10:08:09,588 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: prepare_workspace
2025-05-30 10:08:09,588 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目ID: 9
2025-05-30 10:08:09,589 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 获取项目信息: 9
2025-05-30 10:08:09,589 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9
2025-05-30 10:08:11,334 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 10:08:11,335 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 9, 'description': None, 'name': 'ai-proxy', 'name_with_namespace': 'Longer / ai-proxy', 'path': 'ai-proxy', 'path_with_namespace': 'Longer/ai-proxy', 'created_at': '2025-05-26T12:38:44.596Z', 'default_branch': 'main', 'tag_list': [], 'topics': [], 'ssh_url_to_repo': 'git@***************:Longer/ai-proxy.git', 'http_url_to_repo': 'http://***************/Longer/ai-proxy.git', 'web_url': 'http://***************/Longer/ai-proxy', 'readme_url': 'http://***************/Longer/ai-proxy/-/blob/main/README.md', 'forks_count': 0, 'avatar_url': None, 'star_count': 0, 'last_activity_at': '2025-05-29T12:49:40.103Z', 'namespace': {'id': 3, 'name': 'Longer', 'path': 'Longer', 'kind': 'user', 'full_path': 'Longer', 'parent_id': None, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'container_registry_image_prefix': '***************:5050/longer/ai-proxy', '_links': {'self': 'http://***************/api/v4/projects/9', 'issues': 'http://***************/api/v4/projects/9/issues', 'merge_requests': 'http://***************/api/v4/projects/9/merge_requests', 'repo_branches': 'http://***************/api/v4/projects/9/repository/branches', 'labels': 'http://***************/api/v4/projects/9/labels', 'events': 'http://***************/api/v4/projects/9/events', 'members': 'http://***************/api/v4/projects/9/members', 'cluster_agents': 'http://***************/api/v4/projects/9/cluster_agents'}, 'packages_enabled': True, 'empty_repo': False, 'archived': False, 'visibility': 'private', 'owner': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer'}, 'resolve_outdated_diff_discussions': False, 'container_expiration_policy': {'cadence': '1d', 'enabled': False, 'keep_n': 10, 'older_than': '90d', 'name_regex': '.*', 'name_regex_keep': None, 'next_run_at': '2025-05-27T12:38:44.985Z'}, 'repository_object_format': 'sha1', 'issues_enabled': True, 'merge_requests_enabled': True, 'wiki_enabled': True, 'jobs_enabled': True, 'snippets_enabled': True, 'container_registry_enabled': True, 'service_desk_enabled': False, 'service_desk_address': None, 'can_create_merge_request_in': True, 'issues_access_level': 'enabled', 'repository_access_level': 'enabled', 'merge_requests_access_level': 'enabled', 'forking_access_level': 'enabled', 'wiki_access_level': 'enabled', 'builds_access_level': 'enabled', 'snippets_access_level': 'enabled', 'pages_access_level': 'private', 'analytics_access_level': 'enabled', 'container_registry_access_level': 'enabled', 'security_and_compliance_access_level': 'private', 'releases_access_level': 'enabled', 'environments_access_level': 'enabled', 'feature_flags_access_level': 'enabled', 'infrastructure_access_level': 'enabled', 'monitor_access_level': 'enabled', 'model_experiments_access_level': 'enabled', 'model_registry_access_level': 'enabled', 'emails_disabled': False, 'emails_enabled': True, 'shared_runners_enabled': True, 'lfs_enabled': True, 'creator_id': 3, 'import_status': 'none', 'open_issues_count': 1, 'description_html': '', 'updated_at': '2025-05-29T13:23:13.427Z', 'ci_config_path': None, 'public_jobs': True, 'shared_with_groups': [], 'only_allow_merge_if_pipeline_succeeds': False, 'allow_merge_on_skipped_pipeline': None, 'request_access_enabled': True, 'only_allow_merge_if_all_discussions_are_resolved': False, 'remove_source_branch_after_merge': True, 'printing_merge_request_link_enabled': True, 'merge_method': 'merge', 'squash_option': 'default_off', 'enforce_auth_checks_on_uploads': True, 'suggestion_commit_message': None, 'merge_commit_template': None, 'squash_commit_template': None, 'issue_branch_template': None, 'warn_about_potentially_unwanted_characters': True, 'autoclose_referenced_issues': True, 'max_artifacts_size': None, 'requirements_enabled': False, 'requirements_access_level': 'enabled', 'security_and_compliance_enabled': True, 'compliance_frameworks': [], 'permissions': {'project_access': {'access_level': 30, 'notification_level': 3}, 'group_access': None}}
2025-05-30 10:08:11,336 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:11,336 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 检查本地仓库
2025-05-30 10:08:11,337 - bot_agent.engines.task_executor - INFO - task_executor.py:135 - _prepare_workspace - 使用现有仓库: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:11,338 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: prepare_workspace - 使用现有仓库
2025-05-30 10:08:11,338 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: prepare_workspace，执行时间: 1.75s
2025-05-30 10:08:11,338 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:132 - start_session - Started conversation session: task_1748570891_1748570891
2025-05-30 10:08:11,338 - bot_agent.engines.task_executor - INFO - task_executor.py:773 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-30 10:08:11,339 - bot_agent.engines.task_executor - INFO - task_executor.py:777 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-30 10:08:11,341 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 📍 注册监控点: job_failure_analysis
2025-05-30 10:08:11,341 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始作业失败分析
2025-05-30 10:08:11,342 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 提取Job ID
2025-05-30 10:08:11,342 - bot_agent.engines.task_executor - INFO - task_executor.py:1063 - _handle_job_failure_analysis - 提取到Job ID: 958
2025-05-30 10:08:11,343 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 成功提取Job ID: 958
2025-05-30 10:08:11,343 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取项目ID
2025-05-30 10:08:11,344 - bot_agent.engines.task_executor - INFO - task_executor.py:1071 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-30 10:08:11,344 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 获取到项目ID: 9
2025-05-30 10:08:11,344 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 准备工作空间
2025-05-30 10:08:11,345 - bot_agent.engines.task_executor - INFO - task_executor.py:1086 - _handle_job_failure_analysis - 作业失败分析使用项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:11,346 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 项目路径: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:11,347 - bot_agent.engines.task_executor - INFO - task_executor.py:1092 - _handle_job_failure_analysis - 已更新会话 task_1748570891_1748570891 的项目路径为: E:\aider-git-repos\ai-proxy
2025-05-30 10:08:11,347 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 会话路径已更新
2025-05-30 10:08:11,347 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 开始智能分析
2025-05-30 10:08:11,348 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 预执行数据收集
2025-05-30 10:08:11,355 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://***************/api/v4
2025-05-30 10:08:11,356 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://***************/api/v4/version
2025-05-30 10:08:11,742 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:89 - _check_connection - Successfully connected to GitLab API: {'version': '17.11.1-ee', 'revision': 'cd4c186e8bc', 'kas': {'enabled': True, 'externalUrl': 'ws://***************/-/kubernetes-agent/', 'externalK8sProxyUrl': 'http://***************/-/kubernetes-agent/k8s-proxy/', 'version': '17.11.1'}, 'enterprise': True}
2025-05-30 10:08:11,743 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:65 - __init__ - IntelligentToolCoordinator initialized
2025-05-30 10:08:11,845 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:70 - get_job_info_and_log - 🔍 获取Job 958的信息和日志...
2025-05-30 10:08:11,846 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:437 - get_job - Getting job 958 in project 9
2025-05-30 10:08:11,846 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://***************/api/v4/projects/9/jobs/958
2025-05-30 10:08:12,972 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 200
2025-05-30 10:08:12,973 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:169 - _make_request - Response JSON: {'id': 958, 'status': 'failed', 'stage': 'test', 'name': 'lint', 'ref': 'aider-plus-dev', 'tag': False, 'coverage': None, 'allow_failure': False, 'created_at': '2025-05-30T02:06:09.369Z', 'started_at': '2025-05-30T02:06:18.429Z', 'finished_at': '2025-05-30T02:07:27.040Z', 'erased_at': None, 'duration': 68.61084, 'queued_duration': 7.275693, 'user': {'id': 3, 'username': 'Longer', 'name': 'Longer', 'state': 'active', 'locked': False, 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'web_url': 'http://***************/Longer', 'created_at': '2025-04-27T02:44:28.626Z', 'bio': '', 'location': '', 'public_email': None, 'skype': '', 'linkedin': '', 'twitter': '', 'discord': '', 'website_url': '', 'organization': '', 'job_title': '', 'pronouns': None, 'bot': False, 'work_information': None, 'followers': 0, 'following': 0, 'local_time': None}, 'commit': {'id': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'short_id': 'a030395a', 'created_at': '2025-05-29T21:23:06.000+08:00', 'parent_ids': ['c49d8f5ca7367ab29d3d3b718511b40a29646078'], 'title': 'AI自动修改: 作业失败分析 - lint (Job 952)', 'message': 'AI自动修改: 作业失败分析 - lint (Job 952)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'authored_date': '2025-05-29T21:23:06.000+08:00', 'committer_name': 'Longer (aider)', 'committer_email': '<EMAIL>', 'committed_date': '2025-05-29T21:23:06.000+08:00', 'trailers': {}, 'extended_trailers': {}, 'web_url': 'http://***************/Longer/ai-proxy/-/commit/a030395ae72ef6ad40e5dd194a6cd476b17d3404'}, 'pipeline': {'id': 257, 'iid': 79, 'project_id': 9, 'sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'ref': 'aider-plus-dev', 'status': 'failed', 'source': 'push', 'created_at': '2025-05-29T13:23:17.231Z', 'updated_at': '2025-05-30T02:07:29.869Z', 'web_url': 'http://***************/Longer/ai-proxy/-/pipelines/257'}, 'failure_reason': 'script_failure', 'web_url': 'http://***************/Longer/ai-proxy/-/jobs/958', 'project': {'ci_job_token_scope_enabled': False}, 'artifacts': [], 'runner': {'id': 1, 'description': 'docker-runner-137', 'ip_address': None, 'active': True, 'paused': False, 'is_shared': True, 'runner_type': 'instance_type', 'name': None, 'online': True, 'status': 'online'}, 'runner_manager': {'id': 2, 'system_id': 'r_zhwE8WyyfNZL', 'version': '17.11.0', 'revision': '0f67ff19', 'platform': 'linux', 'architecture': 'amd64', 'created_at': '2025-04-27T09:00:17.697Z', 'contacted_at': '2025-05-30T02:07:28.255Z', 'ip_address': '***************', 'status': 'online'}, 'artifacts_expire_at': None, 'archived': False, 'tag_list': []}
2025-05-30 10:08:12,974 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:444 - get_job - 成功获取作业信息: Job 958 - lint (failed)
2025-05-30 10:08:12,974 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:466 - get_job_log - Getting log for job 958 in project 9
2025-05-30 10:08:13,428 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:484 - get_job_log - 成功获取作业日志: Job 958, 长度: 6609 字符
2025-05-30 10:08:13,429 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:126 - analyze_job_errors - 🔍 开始智能错误分析...
2025-05-30 10:08:13,429 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:535 - analyze_job_log - 🔍 开始分析作业日志...
2025-05-30 10:08:13,431 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:262 - analyze_logs - 开始分析日志文件: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp8ua2d97a.log']
2025-05-30 10:08:13,449 - bot_agent.tools.log_analysis_tools - INFO - log_analysis_tools.py:550 - analyze_job_log - ✅ 作业日志分析完成
2025-05-30 10:08:13,451 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 调用AI进行智能分析
2025-05-30 10:08:13,451 - bot_agent.engines.task_executor - INFO - task_executor.py:1198 - _handle_job_failure_analysis - 🧠 开始使用Bot推理分析器进行作业失败分析...
2025-05-30 10:08:13,460 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:44 - __init__ - BotReasoningAnalyzer initialized with model: deepseek/deepseek-r1:free
2025-05-30 10:08:13,461 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:60 - analyze_job_failure - 🧠 开始Bot级别推理分析...
2025-05-30 10:08:14,029 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:155 - _call_reasoning_model - 🧠 调用推理模型: deepseek/deepseek-r1:free
2025-05-30 10:08:27,987 - bot_agent.analyzers.bot_reasoning_analyzer - INFO - bot_reasoning_analyzer.py:175 - _call_reasoning_model - 🧠 推理模型响应长度: 933 字符
2025-05-30 10:08:27,988 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - Bot推理分析完成，置信度: 0.95
2025-05-30 10:08:27,991 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 启动智能修复协调器
2025-05-30 10:08:27,991 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - 执行智能修复路由分析
2025-05-30 10:08:28,004 - bot_agent.executors.aider_executor - INFO - aider_executor.py:57 - _check_aider_availability - Aider模块可用
2025-05-30 10:08:28,004 - bot_agent.executors.aider_executor - INFO - aider_executor.py:44 - __init__ - AiderExecutor initialized
2025-05-30 10:08:28,004 - bot_agent.executors.aider_executor - INFO - aider_executor.py:57 - _check_aider_availability - Aider模块可用
2025-05-30 10:08:28,005 - bot_agent.executors.aider_executor - INFO - aider_executor.py:44 - __init__ - AiderExecutor initialized
2025-05-30 10:08:28,006 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:46 - execute_intelligent_fix - 🚀 启动智能修复协调器...
2025-05-30 10:08:28,008 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:99 - analyze_and_route - 🧠 开始智能修复路由分析...
2025-05-30 10:08:28,579 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:08:28,580 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:08:28,580 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:08:28,580 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '6b3c4cf2-a93c-4bfb-b1d5-e5841bc7940b', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'a25a4728-c896-40f4-874d-5a92baa6d5c4', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '3fdc0cfc-76d6-4343-8d96-37e0c390b749', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3463'}
2025-05-30 10:08:28,581 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:08:28,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:08:28,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:08:28,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:08:28,581 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '6b3c4cf2-a93c-4bfb-b1d5-e5841bc7940b', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': 'a25a4728-c896-40f4-874d-5a92baa6d5c4', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '3fdc0cfc-76d6-4343-8d96-37e0c390b749', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3463'}
2025-05-30 10:08:28,582 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 257, 'iid': 79, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'before_sha': 'c49d8f5ca7367ab29d3d3b718511b40a29646078', 'source': 'push', 'status': 'failed', 'detailed_status': 'failed', 'stages': ['test', 'build'], 'created_at': '2025-05-29 13:23:17 UTC', 'finished_at': '2025-05-30 02:07:29 UTC', 'duration': 456, 'queued_duration': 8, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/257'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'message': 'AI自动修改: 作业失败分析 - lint (Job 952)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 952)', 'timestamp': '2025-05-29T21:23:06+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 955, 'stage': 'build', 'name': 'build', 'status': 'skipped', 'created_at': '2025-05-29 13:23:17 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 953, 'stage': 'test', 'name': 'test', 'status': 'failed', 'created_at': '2025-05-29 13:23:17 UTC', 'started_at': '2025-05-29 13:23:22 UTC', 'finished_at': '2025-05-29 13:29:49 UTC', 'duration': 387.394854, 'queued_duration': 2.257868, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 958, 'stage': 'test', 'name': 'lint', 'status': 'failed', 'created_at': '2025-05-30 02:06:09 UTC', 'started_at': '2025-05-30 02:06:18 UTC', 'finished_at': '2025-05-30 02:07:27 UTC', 'duration': 68.61084, 'queued_duration': 7.275693, 'failure_reason': 'script_failure', 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-30 10:08:28,583 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-30 10:08:28,583 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-30 10:08:28,583 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 257 for aider-plus-dev is failed (Project: ai-proxy, User: Longer)
2025-05-30 10:08:28,583 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 257 status failed recorded (no AI monitoring needed)
2025-05-30 10:08:28,584 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 257 status failed recorded'}
2025-05-30 10:08:48,863 - bot_agent.tools.intelligent_fix_router - WARNING - intelligent_fix_router.py:231 - _deep_reasoning_analysis - 推理模型响应JSON解析失败，使用文本分析
2025-05-30 10:08:48,864 - bot_agent.tools.intelligent_fix_router - INFO - intelligent_fix_router.py:107 - analyze_and_route - 🎯 修复决策: automated (复杂度: simple, 置信度: 0.60)
2025-05-30 10:08:48,866 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:132 - _execute_automated_fix - 🤖 执行自动化修复策略...
2025-05-30 10:08:48,867 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:149 - _execute_automated_fix - 🔧 执行命令 1/2: sed -i 's/#.*//g' .flake8
2025-05-30 10:08:52,096 - bot_agent.tools.intelligent_fix_coordinator - INFO - intelligent_fix_coordinator.py:149 - _execute_automated_fix - 🔧 执行命令 2/2: sed -i '/^$/d' .flake8
2025-05-30 10:08:54,263 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1703 - verify_fixes - ✅ 开始验证修复效果...
2025-05-30 10:08:57,718 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "black --check --config pyproject.toml ."
2025-05-30 10:09:00,308 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "flake8 ."
2025-05-30 10:09:00,309 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\Projects\aider-plus\venv\Scripts\flake8.exe\__main__.py", line 7, in <module>
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\cli.py", line 23, in main
    app.run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 198, in run
    self._run(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 186, in _run
    self.initialize(argv)
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\main\application.py", line 165, in initialize
    self.plugins, self.options = parse_args(argv)
                                 ^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\parse_args.py", line 53, in parse_args
    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\aggregator.py", line 30, in aggregate_options
    parsed_config = config.parse_config(manager, cfg, cfg_dir)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Projects\aider-plus\venv\Lib\site-packages\flake8\options\config.py", line 131, in parse_config
    raise ValueError(
ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'

2025-05-30 10:09:02,526 - bot_agent - ERROR - logging_config.py:143 - log_command_execution - 命令执行失败: powershell -Command "python -m py_compile ""example.py"" ""setup.py"" ""api_proxy\config.py"" ""api_proxy\health_check.py"" ""api_proxy\job_analysis.py"" ""api_proxy\job_failure_analysis.py"" ""api_proxy\job_lint_analysis.py"" ""api_proxy\job_lint_service.py"" ""api_proxy\models.py"" ""api_proxy\monitoring.py"""
2025-05-30 10:09:02,526 - bot_agent - ERROR - logging_config.py:145 - log_command_execution - 错误信息: [Errno 2] No such file or directory: 'example.py setup.py api_proxy\\config.py api_proxy\\health_check.py api_proxy\\job_analysis.py api_proxy\\job_failure_analysis.py api_proxy\\job_lint_analysis.py api_proxy\\job_lint_service.py api_proxy\\models.py api_proxy\\monitoring.py'
2025-05-30 10:09:02,527 - bot_agent.config.model_config - INFO - model_config.py:141 - log_current_config - 当前AI模型配置:
2025-05-30 10:09:02,528 - bot_agent.config.model_config - INFO - model_config.py:142 - log_current_config -   - 推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-30 10:09:02,528 - bot_agent.config.model_config - INFO - model_config.py:143 - log_current_config -   - 代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 10:09:02,528 - bot_agent.config.model_config - INFO - model_config.py:144 - log_current_config -   - 通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-30 10:09:02,529 - bot_agent.config.model_config - INFO - model_config.py:145 - log_current_config -   - API密钥已设置: 是
2025-05-30 10:09:02,529 - bot_agent.config.model_config - INFO - model_config.py:135 - validate_config - 模型配置验证结果: {'api_key': True, 'analysis_model': True, 'code_generation_model': True}
2025-05-30 10:09:02,529 - bot_agent.config.model_config - INFO - model_config.py:150 - log_current_config - ✅ 所有模型配置正常
2025-05-30 10:09:02,530 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:48 - __init__ - AI Task analyzer initialized with analysis model: deepseek/deepseek-r1:free
2025-05-30 10:09:15,850 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:181 - _ai_analyze_task - AI分析响应: ```json
{
  "task_type": "project_analysis",
  "confidence": 0.8,
  "priority": "high",
  "complexity": "medium",
  "reasoning": "任务核心是对修复效果的验证结果进行分析而非直接执行修复，属于项目质量评估范畴。修复步骤数为0且成功率0%表明修复方案未生效，需要定位lint配置有效性、规则适用性或修复方法正确性等问题。需要分析失败原因并给出改进建议，属于需要技术判断的分析类任务。",
  "risks": [
    {
      "type": "build_failure",
      "level": "medium",
      "description": "lint验证持续失败可能导致CI/CD流程中断"
    },
    {
      "type": "technical_debt",
      "level": "medium",
      "description": "未解决的lint问题可能积累成代码质量隐患"
    },
    {
      "type": "configuration_error",
      "level": "medium",
      "description": "可能存在lint规则配置不当或环境依赖问题"
    }
  ]
}
```
2025-05-30 10:09:15,851 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:197 - _ai_analyze_task - AI任务分析成功: {'task_type': 'project_analysis', 'confidence': 0.8, 'priority': 'high', 'complexity': 'medium', 'reasoning': '任务核心是对修复效果的验证结果进行分析而非直接执行修复，属于项目质量评估范畴。修复步骤数为0且成功率0%表明修复方案未生效，需要定位lint配置有效性、规则适用性或修复方法正确性等问题。需要分析失败原因并给出改进建议，属于需要技术判断的分析类任务。', 'risks': [{'type': 'build_failure', 'level': 'medium', 'description': 'lint验证持续失败可能导致CI/CD流程中断'}, {'type': 'technical_debt', 'level': 'medium', 'description': '未解决的lint问题可能积累成代码质量隐患'}, {'type': 'configuration_error', 'level': 'medium', 'description': '可能存在lint规则配置不当或环境依赖问题'}]}
2025-05-30 10:09:15,854 - bot_agent.engines.task_executor - INFO - task_executor.py:1853 - _evaluate_fix_completeness - 🔍 验证完全失败，需要额外修复
2025-05-30 10:09:15,854 - bot_agent.engines.task_executor - WARNING - task_executor.py:1412 - _handle_job_failure_analysis - 🔄 检测到修复不完整，启动AI驱动的多轮修复...
2025-05-30 10:09:15,854 - bot_agent.engines.task_executor - INFO - task_executor.py:1939 - _execute_ai_driven_multi_round_fix - 🤖 启动AI驱动的多轮修复...
2025-05-30 10:09:15,855 - bot_agent.engines.task_executor - INFO - task_executor.py:1950 - _execute_ai_driven_multi_round_fix - 🎯 发现 2 个剩余错误，开始AI驱动修复
2025-05-30 10:09:15,855 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-30 10:09:15,855 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:519 - _ai_generate_fix_plan - 🤖 AI正在分析错误并生成修复方案...
2025-05-30 10:09:15,858 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:524 - _ai_generate_fix_plan - 📋 收集项目上下文信息...
2025-05-30 10:09:15,858 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:58 - collect_full_context - 🔍 开始收集项目上下文信息: E:\aider-git-repos\ai-proxy
2025-05-30 10:09:19,336 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors"
2025-05-30 10:09:21,445 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "Get-WmiObject -Class Win32_ComputerSystem | Select-Object TotalPhysicalMemory"
2025-05-30 10:09:21,446 - bot_agent.tools.project_context_collector - INFO - project_context_collector.py:78 - collect_full_context - ✅ 项目上下文收集完成，共收集 8 个维度的信息
2025-05-30 10:09:21,446 - bot_agent.tools.intelligent_tool_coordinator - ERROR - intelligent_tool_coordinator.py:757 - _ai_generate_fix_plan - AI修复方案生成异常: cannot access local variable 'ai_prompt' where it is not associated with a value
2025-05-30 10:09:21,446 - bot_agent.tools.intelligent_tool_coordinator - WARNING - intelligent_tool_coordinator.py:759 - _ai_generate_fix_plan - AI修复方案生成异常，使用智能fallback策略
2025-05-30 10:09:21,447 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:834 - _create_intelligent_fallback_plan_async - 🧠 创建智能fallback修复计划...
2025-05-30 10:09:21,447 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:838 - _create_intelligent_fallback_plan_async - 🔍 原始日志长度: 0
2025-05-30 10:09:21,447 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:840 - _create_intelligent_fallback_plan_async - 🎯 识别的错误模式: {'flake8_config': [{'type': 'flake8_config_error', 'severity': 'medium', 'category': 'lint', 'line_number': 92, 'file_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp8ua2d97a.log', 'content': "ValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'", 'solutions': ['检查.flake8配置文件', '修复extend-ignore选项格式', '确保错误代码格式正确(如E203,W503)', '移除无效的错误代码'], 'timestamp': None}]}
2025-05-30 10:09:21,448 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:220 - execute_targeted_fixes - 🔧 执行修复步骤 1/1: 修复flake8配置文件中的extend-ignore选项
2025-05-30 10:09:21,448 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1339 - _execute_ai_fix_step - 🔧 执行AI修复步骤: 修复flake8配置文件中的extend-ignore选项
2025-05-30 10:09:21,448 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1340 - _execute_ai_fix_step - 📝 执行命令: python -c "import os; f=os.path.join('E:/aider-git-repos/ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"
2025-05-30 10:09:21,448 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1371 - _execute_command_with_retry - 🔄 第 1 次尝试执行: python -c "import os; f=os.path.join('E:/aider-git-repos/ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"
2025-05-30 10:09:23,532 - bot_agent - INFO - logging_config.py:139 - log_command_execution - 命令执行成功: powershell -Command "python -c ""import os; f=os.path.join('E:/aider-git-repos/ai-proxy', '.flake8'); content=open(f).read() if os.path.exists(f) else '[flake8]\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('Ignore,', '').replace(',Ignore', '').replace('Ignore', ''))"""
2025-05-30 10:09:23,533 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:1392 - _execute_command_with_retry - ✅ 第 1 次尝试成功
2025-05-30 10:09:23,535 - bot_agent.engines.task_executor - INFO - task_executor.py:1426 - _handle_job_failure_analysis - ✅ AI驱动的多轮修复成功
2025-05-30 10:09:23,536 - bot_agent.engines.task_executor - WARNING - task_executor.py:1433 - _handle_job_failure_analysis - 🔄 验证失败或成功率过低，启动第二轮智能修复...
2025-05-30 10:09:23,536 - bot_agent.engines.task_executor - INFO - task_executor.py:2139 - _execute_second_round_fix - 🔄 启动第二轮智能修复...
2025-05-30 10:09:23,536 - bot_agent.engines.task_executor - INFO - task_executor.py:2153 - _execute_second_round_fix - 🤖 使用AI分析验证失败原因...
2025-05-30 10:09:23,537 - bot_agent.engines.task_executor - ERROR - task_executor.py:2180 - _execute_second_round_fix - AI分析验证失败原因异常: 'IntelligentToolCoordinator' object has no attribute '_ai_analyze_verification_failures'
2025-05-30 10:09:23,537 - bot_agent.engines.task_executor - INFO - task_executor.py:2185 - _execute_second_round_fix - 🎯 第二轮修复目标：3 个剩余错误
2025-05-30 10:09:23,538 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-30 10:09:23,541 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-30 10:09:23,545 - bot_agent.tools.intelligent_tool_coordinator - INFO - intelligent_tool_coordinator.py:189 - execute_targeted_fixes - 🤖 开始AI驱动的智能修复...
2025-05-30 10:09:23,553 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - 🔄 更新监控点: job_failure_analysis - AI智能分析完成
2025-05-30 10:09:23,554 - bot_agent.engines.task_executor - INFO - task_executor.py:1529 - _handle_job_failure_analysis - ✅ AI智能分析完成，保留原始AI响应
2025-05-30 10:09:23,556 - bot_agent.utils.conversation_logger - INFO - conversation_logger.py:277 - end_session - Ended conversation session: task_1748570891_1748570891, status: ConversationStatus.SUCCESS
2025-05-30 10:09:23,557 - bot_agent.utils.deadlock_monitor - INFO - deadlock_monitor.py:268 - _force_log - ✅ 注销监控点: job_failure_analysis，执行时间: 72.22s
2025-05-30 10:09:24,201 - bot_agent.engines.task_executor - INFO - task_executor.py:316 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 958)
2025-05-30 10:09:34,257 - bot_agent.engines.task_executor - INFO - task_executor.py:342 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-30 10:09:34,258 - bot_agent.memory.memory_analyzer - INFO - memory_analyzer.py:62 - analyze_task_completion - Analyzed task completion: 6 global, 1 project memories
2025-05-30 10:09:34,260 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: task_preference
2025-05-30 10:09:34,270 - bot_agent.memory.global_memory - INFO - global_memory.py:90 - save_work_habit - Work habit saved: tool_preference
2025-05-30 10:09:34,271 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: file_creation
2025-05-30 10:09:34,282 - bot_agent.memory.global_memory - INFO - global_memory.py:136 - save_common_pattern - Common pattern saved: directory_structure
2025-05-30 10:09:34,284 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-30 10:09:34,296 - bot_agent.memory.global_memory - INFO - global_memory.py:109 - save_preference - Preference saved: programming_languages
2025-05-30 10:09:34,297 - bot_agent.memory.project_memory - INFO - project_memory.py:75 - save_architecture_info - Architecture info saved
2025-05-30 10:09:34,298 - bot_agent.memory.global_memory - INFO - global_memory.py:150 - save_environment_info - Environment info saved
2025-05-30 10:09:34,299 - bot_agent.memory.memory_integration - INFO - memory_integration.py:111 - learn_from_task_completion - Learned from task completion: 6 global, 1 project memories
2025-05-30 10:09:34,299 - bot_agent.handlers.ai_response_handler - WARNING - ai_response_handler.py:104 - _handle_gitlab_response - Unsupported event type: Job Hook
2025-05-30 10:09:34,299 - bot_agent.dispatcher.router - INFO - router.py:111 - _dispatch_to_component - Task 4cd55598-7c1f-49db-be9a-2ca60a75a11d processed by AI processor: success
2025-05-30 10:09:34,300 - bot_agent.webhook.gitlab - INFO - gitlab.py:1237 - handle_job_event - Job event task routed: {'task_id': '4cd55598-7c1f-49db-be9a-2ca60a75a11d', 'target_component': 'aider', 'status': 'accepted', 'message': 'Task 4cd55598-7c1f-49db-be9a-2ca60a75a11d accepted and processed'}
2025-05-30 10:09:34,300 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'success', 'message': 'Job lint (failed) routed to AI for critical_job_failure', 'task_id': '4cd55598-7c1f-49db-be9a-2ca60a75a11d', 'processing_reason': 'critical_job_failure'}
2025-05-30 10:09:41,741 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:41,742 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:41,742 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:41,743 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '6b634420-0a8c-47f0-a881-5c79ad8842b0', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '24e957b5-f7c1-4301-8437-cee5583d0ad1', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '3424f67f-d9eb-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 10:09:41,743 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:41,743 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:41,743 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:41,743 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:41,744 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '6b634420-0a8c-47f0-a881-5c79ad8842b0', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '24e957b5-f7c1-4301-8437-cee5583d0ad1', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '3424f67f-d9eb-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 10:09:41,745 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 959, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 258, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:09:41,745 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:09:41,746 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:09:41,746 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (959) in stage test is created (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:09:41,746 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status created recorded (no AI processing needed)
2025-05-30 10:09:41,746 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status created recorded'}
2025-05-30 10:09:42,056 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:42,056 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:42,057 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:42,058 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'c87a4a1f-3935-4a3d-8db8-a243d898fc2a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '71d496d2-0f08-45ca-ac40-3dd122de1413', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'dfca9f1c-3f44-43e0-a7f7-9ec5733dd3eb', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 10:09:42,058 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:42,058 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:42,058 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:42,059 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:42,059 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'c87a4a1f-3935-4a3d-8db8-a243d898fc2a', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '71d496d2-0f08-45ca-ac40-3dd122de1413', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'dfca9f1c-3f44-43e0-a7f7-9ec5733dd3eb', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1935'}
2025-05-30 10:09:42,059 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 960, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'created', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 258, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:09:42,060 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:09:42,060 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:09:42,061 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (960) in stage test is created (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:09:42,061 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status created recorded (no AI processing needed)
2025-05-30 10:09:42,061 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status created recorded'}
2025-05-30 10:09:42,363 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:42,364 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:42,364 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:42,364 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '7564fba8-712e-4af1-992b-3a8e83e431a7', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '782f9141-5317-4ccf-8506-520f32f4c07f', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'ec73209e-3143-4da6-8534-a94cd28129a7', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-30 10:09:42,365 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:42,365 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:42,365 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:42,366 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:42,366 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '7564fba8-712e-4af1-992b-3a8e83e431a7', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '782f9141-5317-4ccf-8506-520f32f4c07f', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'ec73209e-3143-4da6-8534-a94cd28129a7', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1937'}
2025-05-30 10:09:42,366 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 961, 'build_name': 'build', 'build_stage': 'build', 'build_status': 'created', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': None, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 258, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:09:42,367 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:09:42,368 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:09:42,368 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job build (961) in stage build is created (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:09:42,368 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job build status created recorded (no AI processing needed)
2025-05-30 10:09:42,368 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job build status created recorded'}
2025-05-30 10:09:47,489 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:47,489 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:47,490 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:47,490 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'c1455ddd-bd54-4f44-a994-1415d20c67ee', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '4be9209e-573d-4e00-aec4-5c8f9c5ad380', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b7625605-6058-4b24-a4ec-5d53e9322cc2', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 10:09:47,490 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:47,491 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:47,495 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:47,495 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:47,495 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'c1455ddd-bd54-4f44-a994-1415d20c67ee', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '4be9209e-573d-4e00-aec4-5c8f9c5ad380', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'b7625605-6058-4b24-a4ec-5d53e9322cc2', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 10:09:47,496 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 959, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 3.390897252, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 258, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:09:47,497 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:09:47,497 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:09:47,497 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (959) in stage test is pending (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:09:47,497 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status pending recorded (no AI processing needed)
2025-05-30 10:09:47,497 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status pending recorded'}
2025-05-30 10:09:48,354 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:48,354 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:48,355 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:48,355 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'dbc1523f-5b2c-4627-a36a-088f2d458a25', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '9185eb66-009e-4912-8c17-7e8602b142b7', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '68425131-336c-4bf3-99aa-450d17d3936d', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 10:09:48,356 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:48,356 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:48,356 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:48,357 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:48,357 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'dbc1523f-5b2c-4627-a36a-088f2d458a25', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '9185eb66-009e-4912-8c17-7e8602b142b7', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '68425131-336c-4bf3-99aa-450d17d3936d', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '1942'}
2025-05-30 10:09:48,357 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 960, 'build_name': 'lint', 'build_stage': 'test', 'build_status': 'pending', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': None, 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': None, 'build_finished_at_iso': None, 'build_duration': None, 'build_queued_duration': 0.238346728, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 258, 'runner': None, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:09:48,358 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:09:48,358 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:09:48,359 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job lint (960) in stage test is pending (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:09:48,359 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job lint status pending recorded (no AI processing needed)
2025-05-30 10:09:48,359 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job lint status pending recorded'}
2025-05-30 10:09:49,311 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:49,311 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:49,312 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:49,312 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '7c8fff49-fb4d-4139-9407-e45e4f3b20ab', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'f01f2121-4692-46dd-aaf2-0ed131da8df9', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '43c34d5c-3076-418c-a837-e1f28e21c72f', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2105'}
2025-05-30 10:09:49,313 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:49,313 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:49,313 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:49,313 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:49,314 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '7c8fff49-fb4d-4139-9407-e45e4f3b20ab', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': 'f01f2121-4692-46dd-aaf2-0ed131da8df9', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '43c34d5c-3076-418c-a837-e1f28e21c72f', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2105'}
2025-05-30 10:09:49,314 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 959, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'running', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': '2025-05-30 02:09:49 UTC', 'build_finished_at': None, 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': '2025-05-30T02:09:49Z', 'build_finished_at_iso': None, 'build_duration': 0.680717358, 'build_queued_duration': 4.23929416, 'build_allow_failure': False, 'build_failure_reason': 'unknown_failure', 'pipeline_id': 258, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'created', 'duration': None, 'started_at': None, 'finished_at': None, 'started_at_iso': None, 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:09:49,315 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:09:49,315 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:09:49,316 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (959) in stage test is running (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:09:49,316 - bot_agent.webhook.gitlab - INFO - gitlab.py:1246 - handle_job_event - Job test status running recorded (no AI processing needed)
2025-05-30 10:09:49,316 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Job test status running recorded'}
2025-05-30 10:09:50,041 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:50,042 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:50,042 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:50,042 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'a171d036-ab36-4049-b341-aecdc520c3fe', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '9182e288-1531-41bc-bbdb-a8c2fcc28c0f', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '9de7b3e5-bb27-4f96-8f33-424626303d2b', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3244'}
2025-05-30 10:09:50,043 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:50,043 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:50,044 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:50,044 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:50,044 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'a171d036-ab36-4049-b341-aecdc520c3fe', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '9182e288-1531-41bc-bbdb-a8c2fcc28c0f', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': '9de7b3e5-bb27-4f96-8f33-424626303d2b', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3244'}
2025-05-30 10:09:50,045 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 258, 'iid': 80, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'source': 'push', 'status': 'pending', 'detailed_status': 'pending', 'stages': ['test', 'build'], 'created_at': '2025-05-30 02:09:41 UTC', 'finished_at': None, 'duration': None, 'queued_duration': None, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/258'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 958)', 'timestamp': '2025-05-30T10:09:23+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/3bcececd4b2c6113def2084334dd302c88616226', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 961, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-30 02:09:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 959, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-30 02:09:41 UTC', 'started_at': '2025-05-30 02:09:49 UTC', 'finished_at': None, 'duration': 1.954635652, 'queued_duration': 4.239294, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 960, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-30 02:09:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 2.43424209, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-30 10:09:50,046 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-30 10:09:50,046 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-30 10:09:50,046 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 258 for aider-plus-dev is pending (Project: ai-proxy, User: Longer)
2025-05-30 10:09:50,047 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 258 status pending recorded (no AI monitoring needed)
2025-05-30 10:09:50,047 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 258 status pending recorded'}
2025-05-30 10:09:54,837 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:09:54,837 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:09:54,837 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:09:54,838 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'f695f15b-09f3-47e0-8b84-7b0d19f31605', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '25d686df-9cf6-466e-b27e-56d3bc0d9e25', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'dc50e406-169b-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3242'}
2025-05-30 10:09:54,838 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:09:54,838 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:09:54,839 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:09:54,839 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:09:54,840 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': 'f695f15b-09f3-47e0-8b84-7b0d19f31605', 'x-gitlab-event': 'Pipeline Hook', 'x-gitlab-webhook-uuid': '25d686df-9cf6-466e-b27e-56d3bc0d9e25', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'dc50e406-169b-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '3242'}
2025-05-30 10:09:54,840 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'pipeline', 'object_attributes': {'id': 258, 'iid': 80, 'name': None, 'ref': 'aider-plus-dev', 'tag': False, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'source': 'push', 'status': 'running', 'detailed_status': 'running', 'stages': ['test', 'build'], 'created_at': '2025-05-30 02:09:41 UTC', 'finished_at': None, 'duration': None, 'queued_duration': 9, 'variables': [], 'url': 'http://***************/Longer/ai-proxy/-/pipelines/258'}, 'merge_request': None, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'commit': {'id': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'title': 'AI自动修改: 作业失败分析 - lint (Job 958)', 'timestamp': '2025-05-30T10:09:23+08:00', 'url': 'http://***************/Longer/ai-proxy/-/commit/3bcececd4b2c6113def2084334dd302c88616226', 'author': {'name': 'Longer (aider)', 'email': '<EMAIL>'}}, 'builds': [{'id': 961, 'stage': 'build', 'name': 'build', 'status': 'created', 'created_at': '2025-05-30 02:09:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': None, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 959, 'stage': 'test', 'name': 'test', 'status': 'running', 'created_at': '2025-05-30 02:09:41 UTC', 'started_at': '2025-05-30 02:09:49 UTC', 'finished_at': None, 'duration': 6.057281433, 'queued_duration': 4.239294, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}, {'id': 960, 'stage': 'test', 'name': 'lint', 'status': 'pending', 'created_at': '2025-05-30 02:09:41 UTC', 'started_at': None, 'finished_at': None, 'duration': None, 'queued_duration': 6.537560589, 'failure_reason': None, 'when': 'on_success', 'manual': False, 'allow_failure': False, 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'runner': None, 'artifacts_file': {'filename': None, 'size': None}, 'environment': None}]}
2025-05-30 10:09:54,842 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Pipeline Hook
2025-05-30 10:09:54,842 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Pipeline Hook, object_kind: pipeline, action: unknown
2025-05-30 10:09:54,842 - bot_agent.webhook.gitlab - INFO - gitlab.py:950 - handle_pipeline_event - Processing Pipeline event: Pipeline 258 for aider-plus-dev is running (Project: ai-proxy, User: Longer)
2025-05-30 10:09:54,843 - bot_agent.webhook.gitlab - INFO - gitlab.py:1082 - handle_pipeline_event - Pipeline 258 status running recorded (no AI monitoring needed)
2025-05-30 10:09:54,843 - bot_agent.api.app - INFO - app.py:151 - gitlab_webhook_direct - 处理函数返回结果: {'status': 'recorded', 'message': 'Pipeline 258 status running recorded'}
2025-05-30 10:11:50,975 - bot_agent.api.app - INFO - app.py:138 - gitlab_webhook_direct - === 直接的 GitLab webhook 处理函数被访问 ===
2025-05-30 10:11:50,977 - bot_agent.api.app - INFO - app.py:139 - gitlab_webhook_direct - 请求方法: POST
2025-05-30 10:11:50,977 - bot_agent.api.app - INFO - app.py:140 - gitlab_webhook_direct - 请求路径: /webhook/gitlab/
2025-05-30 10:11:50,977 - bot_agent.api.app - INFO - app.py:141 - gitlab_webhook_direct - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '92435417-9c05-43c1-b896-8d6c1124a425', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '22de5002-ee0e-4869-9fc4-a2c6fe715afd', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'e6acf89c-710b-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2177'}
2025-05-30 10:11:50,979 - bot_agent.api.app - INFO - app.py:149 - gitlab_webhook_direct - 调用 GitLab webhook 处理函数
2025-05-30 10:11:50,979 - bot_agent.webhook.gitlab - INFO - gitlab.py:151 - process_gitlab_webhook - === 处理 GitLab webhook 请求 ===
2025-05-30 10:11:50,979 - bot_agent.webhook.gitlab - INFO - gitlab.py:152 - process_gitlab_webhook - 请求方法: POST
2025-05-30 10:11:50,979 - bot_agent.webhook.gitlab - INFO - gitlab.py:153 - process_gitlab_webhook - 请求路径: /webhook/gitlab/
2025-05-30 10:11:50,980 - bot_agent.webhook.gitlab - INFO - gitlab.py:154 - process_gitlab_webhook - 请求头: {'content-type': 'application/json', 'user-agent': 'GitLab/17.11.1-ee', 'idempotency-key': '92435417-9c05-43c1-b896-8d6c1124a425', 'x-gitlab-event': 'Job Hook', 'x-gitlab-webhook-uuid': '22de5002-ee0e-4869-9fc4-a2c6fe715afd', 'x-gitlab-instance': 'http://***************', 'x-gitlab-event-uuid': 'e6acf89c-710b-**********************', 'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3', 'accept': '*/*', 'connection': 'close', 'host': '**************:8000', 'content-length': '2177'}
2025-05-30 10:11:50,981 - bot_agent.webhook.gitlab - INFO - gitlab.py:159 - process_gitlab_webhook - 请求体: {'object_kind': 'build', 'ref': 'aider-plus-dev', 'tag': False, 'before_sha': 'a030395ae72ef6ad40e5dd194a6cd476b17d3404', 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'retries_count': 0, 'build_id': 959, 'build_name': 'test', 'build_stage': 'test', 'build_status': 'failed', 'build_created_at': '2025-05-30 02:09:41 UTC', 'build_started_at': '2025-05-30 02:09:49 UTC', 'build_finished_at': '2025-05-30 02:11:50 UTC', 'build_created_at_iso': '2025-05-30T02:09:41Z', 'build_started_at_iso': '2025-05-30T02:09:49Z', 'build_finished_at_iso': '2025-05-30T02:11:50Z', 'build_duration': 120.72489, 'build_queued_duration': 4.239294, 'build_allow_failure': False, 'build_failure_reason': 'script_failure', 'pipeline_id': 258, 'runner': {'id': 1, 'description': 'docker-runner-137', 'runner_type': 'instance_type', 'active': True, 'is_shared': True, 'tags': ['Dev-137']}, 'project_id': 9, 'project_name': 'Longer / ai-proxy', 'user': {'id': 3, 'name': 'Longer', 'username': 'Longer', 'avatar_url': 'https://www.gravatar.com/avatar/e112e6e5827776cc6ab348068457d008291c68ee7a904053462249f0202550e0?s=80&d=identicon', 'email': '[REDACTED]'}, 'commit': {'id': 258, 'name': None, 'sha': '3bcececd4b2c6113def2084334dd302c88616226', 'message': 'AI自动修改: 作业失败分析 - lint (Job 958)\n', 'author_name': 'Longer (aider)', 'author_email': '<EMAIL>', 'author_url': 'http://***************/aider-worker-wsl1', 'status': 'running', 'duration': None, 'started_at': '2025-05-30 02:09:51 UTC', 'finished_at': None, 'started_at_iso': '2025-05-30T02:09:51Z', 'finished_at_iso': None}, 'repository': {'name': 'ai-proxy', 'url': 'git@***************:Longer/ai-proxy.git', 'description': None, 'homepage': 'http://***************/Longer/ai-proxy', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'visibility_level': 0}, 'project': {'id': 9, 'name': 'ai-proxy', 'description': None, 'web_url': 'http://***************/Longer/ai-proxy', 'avatar_url': None, 'git_ssh_url': 'git@***************:Longer/ai-proxy.git', 'git_http_url': 'http://***************/Longer/ai-proxy.git', 'namespace': 'Longer', 'visibility_level': 0, 'path_with_namespace': 'Longer/ai-proxy', 'default_branch': 'main', 'ci_config_path': None}, 'environment': None}
2025-05-30 10:11:50,982 - bot_agent.webhook.gitlab - INFO - gitlab.py:163 - process_gitlab_webhook - GitLab 事件类型: Job Hook
2025-05-30 10:11:50,982 - bot_agent.webhook.gitlab - INFO - gitlab.py:173 - process_gitlab_webhook - Received GitLab webhook event: Job Hook, object_kind: build, action: unknown
2025-05-30 10:11:50,983 - bot_agent.webhook.gitlab - INFO - gitlab.py:1121 - handle_job_event - Processing Job event: Job test (959) in stage test is failed (Pipeline: 258, Project: ai-proxy, User: Longer)
2025-05-30 10:11:50,985 - bot_agent.config.model_config - INFO - model_config.py:141 - log_current_config - 当前AI模型配置:
2025-05-30 10:11:50,986 - bot_agent.config.model_config - INFO - model_config.py:142 - log_current_config -   - 推理分析模型: openrouter/deepseek/deepseek-r1:free
2025-05-30 10:11:50,987 - bot_agent.config.model_config - INFO - model_config.py:143 - log_current_config -   - 代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-30 10:11:50,987 - bot_agent.config.model_config - INFO - model_config.py:144 - log_current_config -   - 通用对话模型: openrouter/deepseek/deepseek-chat
2025-05-30 10:11:50,988 - bot_agent.config.model_config - INFO - model_config.py:145 - log_current_config -   - API密钥已设置: 是
2025-05-30 10:11:50,989 - bot_agent.config.model_config - INFO - model_config.py:135 - validate_config - 模型配置验证结果: {'api_key': True, 'analysis_model': True, 'code_generation_model': True}
2025-05-30 10:11:50,990 - bot_agent.config.model_config - INFO - model_config.py:150 - log_current_config - ✅ 所有模型配置正常
2025-05-30 10:11:50,990 - bot_agent.dispatcher.task_analyzer - INFO - task_analyzer.py:48 - __init__ - AI Task analyzer initialized with analysis model: deepseek/deepseek-r1:free
2025-05-30 10:11:50,991 - bot_agent.dispatcher.router - INFO - router.py:24 - __init__ - Task router initialized
