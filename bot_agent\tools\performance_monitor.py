"""
性能监控和缓存系统 - 优化智能修复架构的性能

核心功能：
1. 监控修复执行性能
2. 缓存分析结果和修复方案
3. 控制并发调用
4. 提供性能指标和优化建议
"""

import logging
import time
import asyncio
import hashlib
import json
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict
import threading

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    cache_hit: bool = False
    memory_usage: Optional[float] = None
    error_message: Optional[str] = None

class PerformanceCache:
    """性能缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
    
    def _generate_key(self, data: Any) -> str:
        """生成缓存键"""
        try:
            if isinstance(data, dict):
                # 对字典进行排序以确保一致的键
                sorted_data = json.dumps(data, sort_keys=True, ensure_ascii=False)
            else:
                sorted_data = str(data)
            
            return hashlib.md5(sorted_data.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"生成缓存键失败: {e}")
            return str(hash(str(data)))
    
    def get(self, key_data: Any) -> Optional[Any]:
        """获取缓存值"""
        try:
            with self.lock:
                key = self._generate_key(key_data)
                
                if key not in self.cache:
                    return None
                
                # 检查是否过期
                cache_time, value = self.cache[key]
                if time.time() - cache_time > self.ttl:
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
                    return None
                
                # 更新访问时间
                self.access_times[key] = time.time()
                return value
                
        except Exception as e:
            logger.error(f"获取缓存失败: {e}")
            return None
    
    def set(self, key_data: Any, value: Any):
        """设置缓存值"""
        try:
            with self.lock:
                key = self._generate_key(key_data)
                
                # 如果缓存已满，删除最久未访问的项
                if len(self.cache) >= self.max_size:
                    self._evict_lru()
                
                self.cache[key] = (time.time(), value)
                self.access_times[key] = time.time()
                
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
    
    def _evict_lru(self):
        """删除最久未访问的缓存项"""
        try:
            if not self.access_times:
                return
            
            # 找到最久未访问的键
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            
            # 删除缓存项
            if lru_key in self.cache:
                del self.cache[lru_key]
            del self.access_times[lru_key]
            
        except Exception as e:
            logger.error(f"LRU淘汰失败: {e}")
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'ttl': self.ttl,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_total_requests', 1), 1)
            }

class ConcurrencyController:
    """并发控制器"""
    
    def __init__(self, max_concurrent_ai_calls: int = 3):
        self.max_concurrent_ai_calls = max_concurrent_ai_calls
        self.ai_call_semaphore = asyncio.Semaphore(max_concurrent_ai_calls)
        self.active_calls = 0
        self.lock = asyncio.Lock()
    
    async def acquire_ai_call_slot(self):
        """获取AI调用槽位"""
        await self.ai_call_semaphore.acquire()
        async with self.lock:
            self.active_calls += 1
    
    async def release_ai_call_slot(self):
        """释放AI调用槽位"""
        self.ai_call_semaphore.release()
        async with self.lock:
            self.active_calls = max(0, self.active_calls - 1)
    
    async def get_active_calls(self) -> int:
        """获取活跃调用数"""
        async with self.lock:
            return self.active_calls

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = []
        self.cache = PerformanceCache()
        self.concurrency_controller = ConcurrencyController()
        self.operation_stats = defaultdict(list)
        self.lock = threading.RLock()
    
    def start_operation(self, operation_name: str) -> str:
        """开始操作监控"""
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        return operation_id
    
    def record_metric(self, metric: PerformanceMetrics):
        """记录性能指标"""
        try:
            with self.lock:
                self.metrics.append(metric)
                self.operation_stats[metric.operation_name].append(metric)
                
                # 保持最近1000条记录
                if len(self.metrics) > 1000:
                    self.metrics = self.metrics[-1000:]
                
                # 每个操作保持最近100条记录
                if len(self.operation_stats[metric.operation_name]) > 100:
                    self.operation_stats[metric.operation_name] = self.operation_stats[metric.operation_name][-100:]
                    
        except Exception as e:
            logger.error(f"记录性能指标失败: {e}")
    
    async def cached_operation(self, 
                             operation_name: str,
                             cache_key_data: Any,
                             operation_func: Callable,
                             *args, **kwargs) -> Any:
        """执行带缓存的操作"""
        start_time = time.time()
        cache_hit = False
        success = False
        error_message = None
        result = None
        
        try:
            # 尝试从缓存获取
            cached_result = self.cache.get(cache_key_data)
            if cached_result is not None:
                cache_hit = True
                success = True
                result = cached_result
                logger.info(f"🎯 缓存命中: {operation_name}")
            else:
                # 执行操作
                if asyncio.iscoroutinefunction(operation_func):
                    result = await operation_func(*args, **kwargs)
                else:
                    result = operation_func(*args, **kwargs)
                
                # 缓存结果
                if result is not None:
                    self.cache.set(cache_key_data, result)
                    success = True
                    logger.info(f"💾 缓存存储: {operation_name}")
                
        except Exception as e:
            error_message = str(e)
            logger.error(f"缓存操作失败: {operation_name} - {e}")
        
        finally:
            # 记录性能指标
            end_time = time.time()
            metric = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                success=success,
                cache_hit=cache_hit,
                error_message=error_message
            )
            self.record_metric(metric)
        
        return result
    
    async def controlled_ai_call(self,
                                operation_name: str,
                                ai_call_func: Callable,
                                *args, **kwargs) -> Any:
        """执行受控的AI调用"""
        await self.concurrency_controller.acquire_ai_call_slot()
        
        start_time = time.time()
        success = False
        error_message = None
        result = None
        
        try:
            logger.info(f"🤖 开始AI调用: {operation_name}")
            
            if asyncio.iscoroutinefunction(ai_call_func):
                result = await ai_call_func(*args, **kwargs)
            else:
                result = ai_call_func(*args, **kwargs)
            
            success = True
            logger.info(f"✅ AI调用完成: {operation_name}")
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"❌ AI调用失败: {operation_name} - {e}")
        
        finally:
            await self.concurrency_controller.release_ai_call_slot()
            
            # 记录性能指标
            end_time = time.time()
            metric = PerformanceMetrics(
                operation_name=f"ai_call_{operation_name}",
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                success=success,
                error_message=error_message
            )
            self.record_metric(metric)
        
        return result
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            with self.lock:
                if not self.metrics:
                    return {'message': '暂无性能数据'}
                
                # 计算总体统计
                total_operations = len(self.metrics)
                successful_operations = sum(1 for m in self.metrics if m.success)
                cache_hits = sum(1 for m in self.metrics if m.cache_hit)
                
                # 计算平均执行时间
                avg_duration = sum(m.duration for m in self.metrics) / total_operations
                
                # 按操作类型统计
                operation_summary = {}
                for op_name, op_metrics in self.operation_stats.items():
                    if op_metrics:
                        operation_summary[op_name] = {
                            'count': len(op_metrics),
                            'success_rate': sum(1 for m in op_metrics if m.success) / len(op_metrics),
                            'avg_duration': sum(m.duration for m in op_metrics) / len(op_metrics),
                            'cache_hit_rate': sum(1 for m in op_metrics if m.cache_hit) / len(op_metrics)
                        }
                
                return {
                    'total_operations': total_operations,
                    'success_rate': successful_operations / total_operations,
                    'cache_hit_rate': cache_hits / total_operations,
                    'avg_duration': avg_duration,
                    'operation_summary': operation_summary,
                    'cache_stats': self.cache.get_stats(),
                    'active_ai_calls': self.concurrency_controller.active_calls
                }
                
        except Exception as e:
            logger.error(f"获取性能摘要失败: {e}")
            return {'error': str(e)}
    
    def get_optimization_recommendations(self) -> List[str]:
        """获取优化建议"""
        try:
            recommendations = []
            summary = self.get_performance_summary()
            
            if isinstance(summary, dict) and 'error' not in summary:
                # 基于缓存命中率的建议
                cache_hit_rate = summary.get('cache_hit_rate', 0)
                if cache_hit_rate < 0.3:
                    recommendations.append("🔄 缓存命中率较低，考虑增加缓存大小或调整TTL")
                
                # 基于成功率的建议
                success_rate = summary.get('success_rate', 0)
                if success_rate < 0.8:
                    recommendations.append("⚠️ 操作成功率较低，需要检查错误处理机制")
                
                # 基于执行时间的建议
                avg_duration = summary.get('avg_duration', 0)
                if avg_duration > 30:
                    recommendations.append("⏱️ 平均执行时间较长，考虑优化算法或增加并发")
                
                # 基于具体操作的建议
                for op_name, op_stats in summary.get('operation_summary', {}).items():
                    if op_stats['avg_duration'] > 60:
                        recommendations.append(f"🐌 {op_name} 操作耗时较长，需要优化")
                    
                    if op_stats['success_rate'] < 0.7:
                        recommendations.append(f"❌ {op_name} 操作失败率较高，需要改进")
            
            if not recommendations:
                recommendations.append("✅ 系统性能良好，无需特别优化")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取优化建议失败: {e}")
            return [f"获取优化建议失败: {str(e)}"]
    
    def clear_metrics(self):
        """清空性能指标"""
        with self.lock:
            self.metrics.clear()
            self.operation_stats.clear()
            self.cache.clear()


# 全局性能监控器实例
global_performance_monitor = PerformanceMonitor()
