"""
智能修复路由器 - 决定使用自动化执行器还是Aider进行修复

核心思想：
1. 推理模型负责分析问题复杂度和生成修复策略
2. 简单/模板化问题 → 自动化执行器
3. 复杂/创新性问题 → Aider AI代码生成
4. 充分发挥AI的智能分析和代码生成能力
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class FixComplexity(Enum):
    """修复复杂度枚举"""
    SIMPLE = "simple"           # 简单修复：配置文件、依赖安装等
    MODERATE = "moderate"       # 中等修复：代码格式化、简单逻辑修改
    COMPLEX = "complex"         # 复杂修复：算法实现、架构调整
    CREATIVE = "creative"       # 创新修复：新功能开发、复杂重构

class FixStrategy(Enum):
    """修复策略枚举"""
    AUTOMATED = "automated"     # 自动化执行器
    AIDER_GUIDED = "aider_guided"  # Aider引导修复
    HYBRID = "hybrid"          # 混合策略

@dataclass
class FixDecision:
    """修复决策结果"""
    complexity: FixComplexity
    strategy: FixStrategy
    confidence: float
    reasoning: str
    estimated_time: int  # 预估修复时间（秒）
    requires_human_review: bool
    automated_commands: List[str] = None
    aider_instructions: str = None

class IntelligentFixRouter:
    """智能修复路由器"""

    def __init__(self):
        self.complexity_patterns = {
            # 简单修复模式
            FixComplexity.SIMPLE: [
                "配置文件错误",
                "依赖缺失",
                "环境变量",
                "权限问题",
                "路径错误",
                "版本不匹配"
            ],
            # 中等修复模式
            FixComplexity.MODERATE: [
                "代码格式化",
                "导入错误",
                "语法错误",
                "简单逻辑错误",
                "变量命名",
                "注释缺失"
            ],
            # 复杂修复模式
            FixComplexity.COMPLEX: [
                "算法实现",
                "性能优化",
                "架构调整",
                "设计模式",
                "并发问题",
                "内存泄漏"
            ],
            # 创新修复模式
            FixComplexity.CREATIVE: [
                "新功能开发",
                "重构设计",
                "创新解决方案",
                "复杂集成",
                "全新实现"
            ]
        }

    async def analyze_and_route(self, error_analysis: Dict[str, Any],
                               project_context: Dict[str, Any]) -> FixDecision:
        """
        分析错误并决定修复策略

        Args:
            error_analysis: 错误分析结果
            project_context: 项目上下文信息

        Returns:
            FixDecision: 修复决策
        """
        try:
            logger.info("🧠 开始智能修复路由分析...")

            # 第0步：错误分类和优先级分析
            from .error_classifier import global_error_classifier

            all_errors = error_analysis.get('all_errors', [])
            classified_errors = global_error_classifier.classify_errors(all_errors, project_context)

            if classified_errors:
                # 获取分类摘要
                classification_summary = global_error_classifier.get_classification_summary(classified_errors)
                logger.info(f"📊 错误分类完成: {classification_summary['total_errors']} 个错误")

                # 获取修复顺序
                ordered_errors = global_error_classifier.get_fix_order(classified_errors)

                # 更新错误分析，包含分类信息
                error_analysis['classified_errors'] = [
                    {
                        'original': err.original_error,
                        'category': err.category.value,
                        'priority': err.priority.value,
                        'impact': err.impact.value,
                        'confidence': err.confidence,
                        'description': err.description,
                        'suggested_strategy': err.suggested_fix_strategy,
                        'estimated_time': err.estimated_fix_time
                    }
                    for err in ordered_errors
                ]
                error_analysis['classification_summary'] = classification_summary

            # 第1步：使用推理模型深度分析
            reasoning_result = await self._deep_reasoning_analysis(error_analysis, project_context)

            # 第2步：基于推理结果和分类信息决定策略
            fix_decision = await self._make_fix_decision(reasoning_result, error_analysis)

            # 第3步：根据错误分类调整决策
            if classified_errors:
                fix_decision = self._adjust_decision_by_classification(fix_decision, classified_errors)

            logger.info(f"🎯 修复决策: {fix_decision.strategy.value} (复杂度: {fix_decision.complexity.value}, 置信度: {fix_decision.confidence:.2f})")

            return fix_decision

        except Exception as e:
            logger.error(f"智能修复路由分析失败: {e}")
            # 默认使用自动化策略
            return FixDecision(
                complexity=FixComplexity.SIMPLE,
                strategy=FixStrategy.AUTOMATED,
                confidence=0.5,
                reasoning=f"路由分析失败，使用默认策略: {str(e)}",
                estimated_time=60,
                requires_human_review=True
            )

    async def _deep_reasoning_analysis(self, error_analysis: Dict[str, Any],
                                     project_context: Dict[str, Any]) -> Dict[str, Any]:
        """使用推理模型进行深度分析"""
        try:
            from bot_agent.utils.llm_retry_handler import enhanced_llm_call
            import httpx
            import os

            # 构建推理分析提示词
            reasoning_prompt = f"""
你是一个专业的软件工程专家和问题解决专家。请深度分析以下错误，并评估修复的复杂度和最佳策略。

## 错误信息分析
{json.dumps(error_analysis, ensure_ascii=False, indent=2)}

## 项目上下文
{json.dumps(project_context, ensure_ascii=False, indent=2)}

## 分析要求
请从以下维度进行深度分析：

1. **问题本质分析**：
   - 这是什么类型的问题？
   - 问题的根本原因是什么？
   - 涉及哪些技术领域？

2. **复杂度评估**：
   - simple: 简单配置/依赖问题，可用脚本命令解决
   - moderate: 需要理解代码逻辑，但修改相对直接
   - complex: 需要深度理解业务逻辑和架构设计
   - creative: 需要创新思维和全新实现

3. **修复策略建议**：
   - automated: 使用预定义命令和脚本
   - aider_guided: 需要AI理解代码并生成解决方案
   - hybrid: 结合自动化和AI生成

4. **风险评估**：
   - 修复可能带来的副作用
   - 是否需要人工审查
   - 预估修复时间

请用JSON格式返回分析结果：
{{
    "problem_essence": "问题本质描述",
    "root_cause": "根本原因分析",
    "technical_domains": ["涉及的技术领域"],
    "complexity_score": 0.0-1.0,
    "complexity_reasoning": "复杂度判断理由",
    "recommended_strategy": "automated|aider_guided|hybrid",
    "strategy_reasoning": "策略选择理由",
    "risk_assessment": {{
        "potential_side_effects": ["可能的副作用"],
        "requires_review": true/false,
        "review_reasoning": "是否需要审查的理由"
    }},
    "time_estimation": {{
        "automated_time": 估算秒数,
        "aider_time": 估算秒数,
        "explanation": "时间估算说明"
    }},
    "confidence": 0.0-1.0
}}
"""

            # 调用推理模型
            async def call_reasoning_ai():
                api_key = os.getenv("OPENROUTER_API_KEY")
                if not api_key:
                    raise ValueError("OPENROUTER_API_KEY not found")

                logger.info("🔥 开始调用DeepSeek R1推理模型进行智能路由分析...")

                async with httpx.AsyncClient(timeout=60.0) as client:
                    request_data = {
                        "model": "deepseek/deepseek-r1:free",  # 使用推理模型
                        "messages": [
                            {"role": "user", "content": reasoning_prompt}
                        ],
                        "temperature": 0.1,  # 低温度确保分析的一致性
                        "max_tokens": 2000
                    }

                    logger.info(f"📤 发送请求到OpenRouter API: {request_data['model']}")

                    response = await client.post(
                        "https://openrouter.ai/api/v1/chat/completions",
                        headers={
                            "Authorization": f"Bearer {api_key}",
                            "Content-Type": "application/json"
                        },
                        json=request_data
                    )

                    logger.info(f"📥 收到API响应: status={response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        content = result["choices"][0]["message"]["content"]
                        logger.info(f"✅ DeepSeek R1推理完成，响应长度: {len(content)} 字符")
                        return {"content": content}
                    else:
                        error_text = response.text
                        logger.error(f"❌ API调用失败: {response.status_code}, 响应: {error_text}")
                        raise Exception(f"API调用失败: {response.status_code}, 响应: {error_text}")

            # 使用增强的LLM调用
            logger.info("🚀 启动增强LLM调用...")
            ai_response = await enhanced_llm_call(
                call_reasoning_ai,
                task_info={"title": "智能修复路由分析", "task_type": "reasoning_analysis"}
            )

            if ai_response and ai_response.get('content'):
                # 解析AI响应
                try:
                    reasoning_result = json.loads(ai_response['content'])
                    logger.info("✅ 推理模型分析完成")
                    return reasoning_result
                except json.JSONDecodeError:
                    logger.warning("推理模型响应JSON解析失败，使用文本分析")
                    return self._parse_reasoning_text(ai_response['content'])
            else:
                raise Exception("推理模型响应为空")

        except Exception as e:
            logger.error(f"推理模型分析失败: {e}")
            # 记录详细的失败信息
            logger.error(f"推理模型调用详情 - 错误类型: {type(e).__name__}, 错误信息: {str(e)}")
            # 使用基于规则的fallback分析
            fallback_result = self._rule_based_analysis(error_analysis)
            fallback_result['fallback_reason'] = f"推理模型调用失败: {str(e)}"
            return fallback_result

    async def _make_fix_decision(self, reasoning_result: Dict[str, Any],
                               error_analysis: Dict[str, Any]) -> FixDecision:
        """基于推理结果制定修复决策"""
        try:
            # 提取推理结果
            complexity_score = reasoning_result.get('complexity_score', 0.5)
            recommended_strategy = reasoning_result.get('recommended_strategy', 'automated')
            confidence = reasoning_result.get('confidence', 0.7)

            # 确定复杂度级别
            if complexity_score <= 0.3:
                complexity = FixComplexity.SIMPLE
            elif complexity_score <= 0.6:
                complexity = FixComplexity.MODERATE
            elif complexity_score <= 0.8:
                complexity = FixComplexity.COMPLEX
            else:
                complexity = FixComplexity.CREATIVE

            # 确定修复策略
            if recommended_strategy == "automated" and complexity in [FixComplexity.SIMPLE, FixComplexity.MODERATE]:
                strategy = FixStrategy.AUTOMATED
                # 生成自动化命令
                automated_commands = await self._generate_automated_commands(error_analysis, reasoning_result)
                aider_instructions = None
            elif recommended_strategy == "aider_guided" or complexity in [FixComplexity.COMPLEX, FixComplexity.CREATIVE]:
                strategy = FixStrategy.AIDER_GUIDED
                # 生成Aider指令
                aider_instructions = await self._generate_aider_instructions(error_analysis, reasoning_result)
                automated_commands = None
            else:
                strategy = FixStrategy.HYBRID
                # 生成混合策略
                automated_commands = await self._generate_automated_commands(error_analysis, reasoning_result)
                aider_instructions = await self._generate_aider_instructions(error_analysis, reasoning_result)

            # 时间估算
            time_estimation = reasoning_result.get('time_estimation', {})
            if strategy == FixStrategy.AUTOMATED:
                estimated_time = time_estimation.get('automated_time', 60)
            else:
                estimated_time = time_estimation.get('aider_time', 300)

            # 风险评估
            risk_assessment = reasoning_result.get('risk_assessment', {})
            requires_review = risk_assessment.get('requires_review', complexity in [FixComplexity.COMPLEX, FixComplexity.CREATIVE])

            return FixDecision(
                complexity=complexity,
                strategy=strategy,
                confidence=confidence,
                reasoning=reasoning_result.get('strategy_reasoning', '基于AI推理分析的决策'),
                estimated_time=estimated_time,
                requires_human_review=requires_review,
                automated_commands=automated_commands,
                aider_instructions=aider_instructions
            )

        except Exception as e:
            logger.error(f"制定修复决策失败: {e}")
            # 返回保守的默认决策
            return FixDecision(
                complexity=FixComplexity.MODERATE,
                strategy=FixStrategy.AIDER_GUIDED,
                confidence=0.6,
                reasoning=f"决策制定失败，使用保守策略: {str(e)}",
                estimated_time=300,
                requires_human_review=True
            )

    async def _generate_automated_commands(self, error_analysis: Dict[str, Any],
                                         reasoning_result: Dict[str, Any]) -> List[str]:
        """生成自动化修复命令（支持Windows和Linux）"""
        try:
            import platform
            commands = []
            errors = error_analysis.get('all_errors', [])
            is_windows = platform.system().lower() == 'windows'

            for error in errors:
                error_str = str(error)

                # 基于错误类型生成命令
                if 'flake8' in error_str.lower():
                    if 'extend-ignore' in error_str:
                        if is_windows:
                            # Windows PowerShell命令
                            commands.append('powershell -Command "(Get-Content .flake8 -ErrorAction SilentlyContinue) -replace \'#.*\', \'\' | Where-Object {$_.trim() -ne \'\'} | Set-Content .flake8"')
                        else:
                            # Linux sed命令
                            commands.append("sed -i 's/#.*//g' .flake8")  # 移除注释
                            commands.append("sed -i '/^$/d' .flake8")     # 移除空行
                elif 'black' in error_str.lower():
                    commands.append("black --check .")
                elif 'pytest' in error_str.lower():
                    commands.append("pytest --tb=short")
                elif 'pip install' in error_str.lower():
                    # 提取包名
                    import re
                    match = re.search(r'pip install (\S+)', error_str)
                    if match:
                        package = match.group(1)
                        commands.append(f"pip install {package}")

            if not commands:
                # 如果没有识别到特定命令，生成通用的flake8配置修复
                if any('flake8' in str(e).lower() for e in errors):
                    if is_windows:
                        commands.append('python -c "import os; f=\'.flake8\'; content=open(f).read() if os.path.exists(f) else \'[flake8]\\nextend-ignore = E203,W503\'; open(f, \'w\').write(content.replace(\'#\', \'\').replace(\'Ignore\', \'\'))"')
                    else:
                        commands.append("python3 -c \"import os; f='.flake8'; content=open(f).read() if os.path.exists(f) else '[flake8]\\nextend-ignore = E203,W503'; open(f, 'w').write(content.replace('#', '').replace('Ignore', ''))\"")
                else:
                    commands.append("echo '没有识别到可自动化的修复命令'")

            return commands

        except Exception as e:
            logger.error(f"生成自动化命令失败: {e}")
            return ["echo '自动化命令生成失败'"]

    async def _generate_aider_instructions(self, error_analysis: Dict[str, Any],
                                         reasoning_result: Dict[str, Any]) -> str:
        """生成Aider修复指令"""
        try:
            problem_essence = reasoning_result.get('problem_essence', '未知问题')
            root_cause = reasoning_result.get('root_cause', '原因分析中')

            aider_instruction = f"""
## 🎯 修复任务说明

### 问题描述
{problem_essence}

### 根本原因
{root_cause}

### 修复要求
1. 请仔细分析代码结构和错误原因
2. 提供符合最佳实践的解决方案
3. 确保修复不会引入新的问题
4. 添加必要的注释和文档
5. 如果需要，请提供测试用例

### 错误详情
{json.dumps(error_analysis, ensure_ascii=False, indent=2)}

请用中文详细说明你的修复思路，然后实施修复。
"""

            return aider_instruction

        except Exception as e:
            logger.error(f"生成Aider指令失败: {e}")
            return f"请修复以下错误：{error_analysis}"

    def _rule_based_analysis(self, error_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """基于规则的fallback分析"""
        try:
            errors = error_analysis.get('all_errors', [])
            error_text = ' '.join(str(e) for e in errors).lower()

            # 简单规则匹配
            if any(keyword in error_text for keyword in ['config', 'flake8', 'black', 'pip install']):
                complexity_score = 0.2
                strategy = "automated"
            elif any(keyword in error_text for keyword in ['syntax', 'import', 'format']):
                complexity_score = 0.5
                strategy = "aider_guided"
            else:
                complexity_score = 0.7
                strategy = "aider_guided"

            return {
                "problem_essence": "基于规则的问题分析",
                "root_cause": "使用规则匹配分析",
                "complexity_score": complexity_score,
                "recommended_strategy": strategy,
                "confidence": 0.6,
                "time_estimation": {
                    "automated_time": 60,
                    "aider_time": 300
                },
                "risk_assessment": {
                    "requires_review": complexity_score > 0.6
                }
            }

        except Exception as e:
            logger.error(f"规则分析失败: {e}")
            return {
                "complexity_score": 0.5,
                "recommended_strategy": "aider_guided",
                "confidence": 0.5,
                "time_estimation": {"automated_time": 60, "aider_time": 300},
                "risk_assessment": {"requires_review": True}
            }

    def _parse_reasoning_text(self, text: str) -> Dict[str, Any]:
        """解析推理模型的文本响应"""
        try:
            # 简单的文本解析逻辑
            if "simple" in text.lower() or "配置" in text:
                complexity_score = 0.3
                strategy = "automated"
            elif "complex" in text.lower() or "复杂" in text:
                complexity_score = 0.8
                strategy = "aider_guided"
            else:
                complexity_score = 0.5
                strategy = "aider_guided"

            return {
                "problem_essence": "文本解析结果",
                "complexity_score": complexity_score,
                "recommended_strategy": strategy,
                "confidence": 0.6,
                "time_estimation": {"automated_time": 60, "aider_time": 300},
                "risk_assessment": {"requires_review": complexity_score > 0.6}
            }

        except Exception as e:
            logger.error(f"文本解析失败: {e}")
            return self._rule_based_analysis({})

    def _adjust_decision_by_classification(self, fix_decision: FixDecision, classified_errors) -> FixDecision:
        """根据错误分类调整修复决策"""
        try:
            from .error_classifier import ErrorPriority, ErrorCategory, ErrorImpact

            # 分析错误特征
            has_critical = any(err.priority == ErrorPriority.CRITICAL for err in classified_errors)
            has_blocking = any(err.impact == ErrorImpact.BLOCKING for err in classified_errors)
            has_security = any(err.category == ErrorCategory.SECURITY for err in classified_errors)

            # 统计各类错误数量
            syntax_errors = sum(1 for err in classified_errors if err.category == ErrorCategory.SYNTAX)
            config_errors = sum(1 for err in classified_errors if err.category == ErrorCategory.CONFIGURATION)
            lint_errors = sum(1 for err in classified_errors if err.category == ErrorCategory.LINT)

            # 调整策略
            original_strategy = fix_decision.strategy

            # 如果有关键或阻塞性错误，优先使用Aider
            if has_critical or has_blocking or has_security:
                if original_strategy == FixStrategy.AUTOMATED:
                    fix_decision.strategy = FixStrategy.AIDER_GUIDED
                    fix_decision.reasoning += " | 检测到关键错误，切换到Aider策略"
                    fix_decision.requires_human_review = True

            # 如果主要是配置和代码规范错误，优先使用自动化
            elif config_errors + lint_errors >= len(classified_errors) * 0.8:
                if original_strategy == FixStrategy.AIDER_GUIDED:
                    fix_decision.strategy = FixStrategy.AUTOMATED
                    fix_decision.reasoning += " | 主要为配置错误，切换到自动化策略"
                    fix_decision.estimated_time = min(fix_decision.estimated_time, 120)

            # 如果有语法错误，必须使用Aider
            elif syntax_errors > 0:
                fix_decision.strategy = FixStrategy.AIDER_GUIDED
                fix_decision.reasoning += " | 检测到语法错误，需要AI深度分析"
                fix_decision.requires_human_review = True

            # 调整复杂度
            if has_critical or has_security:
                fix_decision.complexity = FixComplexity.COMPLEX
            elif config_errors + lint_errors == len(classified_errors):
                fix_decision.complexity = FixComplexity.SIMPLE

            # 调整预估时间
            total_estimated_time = sum(err.estimated_fix_time for err in classified_errors)
            if total_estimated_time > 0:
                fix_decision.estimated_time = min(total_estimated_time, fix_decision.estimated_time * 2)

            # 调整置信度
            avg_confidence = sum(err.confidence for err in classified_errors) / len(classified_errors)
            fix_decision.confidence = (fix_decision.confidence + avg_confidence) / 2

            if original_strategy != fix_decision.strategy:
                logger.info(f"🔄 根据错误分类调整策略: {original_strategy.value} → {fix_decision.strategy.value}")

            return fix_decision

        except Exception as e:
            logger.error(f"根据错误分类调整决策失败: {e}")
            return fix_decision
