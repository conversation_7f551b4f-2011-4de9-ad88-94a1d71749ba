{"job:lint|types:flake8_config_error,generic_job_failure|keywords:extend-ignore": {"error_signature": "job:lint|types:flake8_config_error,generic_job_failure|keywords:extend-ignore", "fix_strategy": "automated_fallback_to_aider", "success_count": 6, "failure_count": 0, "avg_execution_time": 4.623576958974202, "last_used": 1748598391.1899312, "complexity_score": 0.2, "commands_used": [], "context_factors": {"project_type": "unknown", "error_count": 3, "has_config_files": []}}, "job:test|types:generic_job_failure,import_error|keywords:": {"error_signature": "job:test|types:generic_job_failure,import_error|keywords:", "fix_strategy": "aider_guided", "success_count": 2, "failure_count": 0, "avg_execution_time": 0.02844858169555664, "last_used": 1748598360.2073455, "complexity_score": 0.8, "commands_used": [], "context_factors": {"project_type": "unknown", "error_count": 24, "has_config_files": []}}}